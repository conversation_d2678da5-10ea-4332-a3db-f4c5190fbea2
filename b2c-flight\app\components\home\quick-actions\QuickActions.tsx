
'use client';

import React, { useState } from 'react';
// import Button from '../../ui/Button'; // Removed unused import

type ColorKey = 'blue' | 'emerald' | 'amber' | 'purple' | 'rose' | 'indigo' | 'cyan' | 'teal' | 'green';
type PriorityKey = 'high' | 'medium' | 'low';
type CategoryKey = 'operations' | 'marketing' | 'finance';

interface ActionItem {
  title: string;
  description: string;
  icon: string;
  color: ColorKey;
  action: () => void;
  priority: PriorityKey;
}

export default function QuickActions() {
  const [activeCategory, setActiveCategory] = useState<CategoryKey>('operations');

  const actionCategories: Record<CategoryKey, ActionItem[]> = {
    operations: [
      {
        title: 'Add Premium Property',
        description: 'Register luxury hotel partner',
        icon: 'ri-building-2-line',
        color: 'blue',
        action: () => console.log('Add premium property'),
        priority: 'high'
      },
      {
        title: 'Bulk Inventory Update',
        description: 'Update room availability across properties',
        icon: 'ri-database-2-line',
        color: 'emerald',
        action: () => console.log('Bulk inventory update'),
        priority: 'medium'
      },
      {
        title: 'Emergency Maintenance',
        description: 'Schedule system maintenance window',
        icon: 'ri-tools-line',
        color: 'amber',
        action: () => console.log('Emergency maintenance'),
        priority: 'high'
      }
    ],
    marketing: [
      {
        title: 'Launch Campaign',
        description: 'Create targeted marketing campaign',
        icon: 'ri-megaphone-line',
        color: 'purple',
        action: () => console.log('Launch campaign'),
        priority: 'medium'
      },
      {
        title: 'Seasonal Promotions',
        description: 'Set up holiday discount packages',
        icon: 'ri-gift-line',
        color: 'rose',
        action: () => console.log('Seasonal promotions'),
        priority: 'medium'
      },
      {
        title: 'Loyalty Program',
        description: 'Manage premium member benefits',
        icon: 'ri-vip-crown-line',
        color: 'indigo',
        action: () => console.log('Loyalty program'),
        priority: 'low'
      }
    ],
    finance: [
      {
        title: 'Revenue Report',
        description: 'Generate comprehensive financial analysis',
        icon: 'ri-bar-chart-box-line',
        color: 'cyan',
        action: () => console.log('Revenue report'),
        priority: 'high'
      },
      {
        title: 'Payment Gateway',
        description: 'Configure new payment methods',
        icon: 'ri-bank-card-line',
        color: 'teal',
        action: () => console.log('Payment gateway'),
        priority: 'medium'
      },
      {
        title: 'Expense Tracking',
        description: 'Monitor operational costs',
        icon: 'ri-money-dollar-circle-line',
        color: 'green',
        action: () => console.log('Expense tracking'),
        priority: 'low'
      }
    ]
  };

  const colorClasses: Record<ColorKey, string> = {
    blue: 'bg-blue-50 border-blue-200 hover:bg-blue-100 hover:border-blue-300',
    emerald: 'bg-emerald-50 border-emerald-200 hover:bg-emerald-100 hover:border-emerald-300',
    amber: 'bg-amber-50 border-amber-200 hover:bg-amber-100 hover:border-amber-300',
    purple: 'bg-purple-50 border-purple-200 hover:bg-purple-100 hover:border-purple-300',
    rose: 'bg-rose-50 border-rose-200 hover:bg-rose-100 hover:border-rose-300',
    indigo: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100 hover:border-indigo-300',
    cyan: 'bg-cyan-50 border-cyan-200 hover:bg-cyan-100 hover:border-cyan-300',
    teal: 'bg-teal-50 border-teal-200 hover:bg-teal-100 hover:border-teal-300',
    green: 'bg-green-50 border-green-200 hover:bg-green-100 hover:border-green-300'
  };

  const iconColors: Record<ColorKey, string> = {
    blue: 'text-blue-600',
    emerald: 'text-emerald-600',
    amber: 'text-amber-600',
    purple: 'text-purple-600',
    rose: 'text-rose-600',
    indigo: 'text-indigo-600',
    cyan: 'text-cyan-600',
    teal: 'text-teal-600',
    green: 'text-green-600'
  };

  const priorityColors: Record<PriorityKey, string> = {
    high: 'bg-red-500',
    medium: 'bg-amber-500',
    low: 'bg-slate-400'
  };

  const categories: Array<{ id: CategoryKey; label: string; icon: string }> = [
    { id: 'operations', label: 'Operations', icon: 'ri-settings-line' },
    { id: 'marketing', label: 'Marketing', icon: 'ri-megaphone-line' },
    { id: 'finance', label: 'Finance', icon: 'ri-bar-chart-line' }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden">
      <div className="flex flex-col gap-4 mb-6">
        <div className="min-w-0">
          <h2 className="text-lg sm:text-xl font-semibold text-slate-900">Quick Actions</h2>
          <p className="text-sm text-slate-500">Streamlined access to key operations</p>
        </div>
        <div className="flex items-center gap-1 bg-slate-100 rounded-xl p-1 overflow-x-auto">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all whitespace-nowrap ${
                activeCategory === category.id
                  ? 'bg-white text-slate-900 shadow-sm'
                  : 'text-slate-600 hover:text-slate-900'
              }`}
            >
              <i className={`${category.icon} mr-2`}></i>
              {category.label}
            </button>
          ))}
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-3 sm:gap-4 mb-6">
        {actionCategories[activeCategory].map((action, index) => (
          <div
            key={index}
            className={`p-4 sm:p-5 rounded-xl border-2 transition-all cursor-pointer group ${colorClasses[action.color as keyof typeof colorClasses]} overflow-hidden`}
            onClick={action.action}
          >
            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
                <div className={`w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-xl bg-white shadow-sm ${iconColors[action.color as keyof typeof iconColors]} flex-shrink-0`}>
                  <i className={`${action.icon} text-lg sm:text-xl`}></i>
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-semibold text-slate-900 text-sm sm:text-base truncate">{action.title}</h3>
                    <div className={`w-2 h-2 rounded-full ${priorityColors[action.priority as keyof typeof priorityColors]} flex-shrink-0`}></div>
                  </div>
                  <p className="text-sm text-slate-600 line-clamp-2">{action.description}</p>
                </div>
              </div>
              <div className="opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0">
                <i className="ri-arrow-right-line text-slate-400"></i>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="space-y-3">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <button className="w-full flex items-center justify-center py-2 sm:py-3 px-4 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors text-sm font-medium">
            <i className="ri-add-line mr-2"></i>
            Custom Action
          </button>
          <button className="w-full flex items-center justify-center py-2 sm:py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
            <i className="ri-dashboard-line mr-2"></i>
            Action Center
          </button>
        </div>

        <div className="pt-3 sm:pt-4 border-t border-slate-200">
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-500">Frequently Used</span>
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                <i className="ri-hotel-line text-blue-600 text-sm"></i>
              </div>
              <div className="w-6 h-6 bg-emerald-100 rounded-lg flex items-center justify-center">
                <i className="ri-user-add-line text-emerald-600 text-sm"></i>
              </div>
              <div className="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                <i className="ri-file-download-line text-purple-600 text-sm"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}