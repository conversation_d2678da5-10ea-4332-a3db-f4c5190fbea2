

import React from 'react';

// Define the props interface for type safety (optional but recommended)
interface PageSectionHeaderprops {
  title: string;
  subtitle: string;
  totalItems?: number;
  showAddButton?: boolean;
  onAddButtonClick?: () => void;
  addButtonText?: string;
  showMetrics?: boolean;
}

const PageSectionHeader: React.FC<PageSectionHeaderprops> = ({
  title,
  subtitle,
  totalItems,
  showAddButton = false,
  onAddButtonClick,
  addButtonText = 'Add Item',
}) => {
  return (
    <div className="w-full bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="flex-1 min-w-0">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
            {title}
          </h2>
          {subtitle && (
            <p className="text-sm text-gray-600 mt-1">
              {subtitle}
            </p>
          )}
        </div>
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          {totalItems && (
            <div className="bg-blue-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-blue-700">
                Total Items: {totalItems}
              </span>
            </div>
          )}

          {showAddButton && (
            <button onClick={onAddButtonClick} className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
              <i className="ri-add-line mr-2"></i>
              {addButtonText}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PageSectionHeader;