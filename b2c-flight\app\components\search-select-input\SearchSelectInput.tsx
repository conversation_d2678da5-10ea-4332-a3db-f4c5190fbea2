import React, { useState, useRef, useEffect } from 'react';

// A reusable component for a searchable select dropdown with keyboard navigation
export interface SearchableSelectProps {
    options: { id: string; name: string }[];
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
    label: string;
    className?: string;
}

export const SearchableSelect: React.FC<SearchableSelectProps> = ({ 
    options, 
    value, 
    onChange, 
    placeholder, 
    label, 
    className 
}) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    
    const wrapperRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const listRef = useRef<HTMLDivElement>(null);

    // Filter options based on the search term
    const filteredOptions = options.filter(option =>
        option.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.id.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const selectedOption = options.find(option => option.id === value);

    // Reset highlighted index when filtered options change
    useEffect(() => {
        setHighlightedIndex(-1);
    }, [searchTerm]);

    // Scroll highlighted option into view
    useEffect(() => {
        if (highlightedIndex >= 0 && listRef.current) {
            const highlightedElement = listRef.current.children[highlightedIndex] as HTMLElement;
            if (highlightedElement) {
                highlightedElement.scrollIntoView({
                    block: 'nearest',
                    behavior: 'smooth'
                });
            }
        }
    }, [highlightedIndex]);

    const handleSelect = (id: string) => {
        onChange(id);
        const selectedOpt = options.find(opt => opt.id === id);
        setSearchTerm(selectedOpt?.name || '');
        setIsOpen(false);
        setHighlightedIndex(-1);
        inputRef.current?.blur();
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setIsOpen(true);
        setHighlightedIndex(-1);
    };

    const handleFocus = () => {
        setIsOpen(true);
        setSearchTerm('');
        setHighlightedIndex(-1);
    };

    const handleBlur = () => {
        // Use a timeout to allow click events on options to fire
        setTimeout(() => {
            if (wrapperRef.current && !wrapperRef.current.contains(document.activeElement)) {
                setIsOpen(false);
                setSearchTerm(selectedOption?.name || '');
                setHighlightedIndex(-1);
            }
        }, 150);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (!isOpen) {
            if (e.key === 'ArrowDown' || e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setIsOpen(true);
                setSearchTerm('');
            }
            return;
        }

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setHighlightedIndex(prev => 
                    prev < filteredOptions.length - 1 ? prev + 1 : 0
                );
                break;

            case 'ArrowUp':
                e.preventDefault();
                setHighlightedIndex(prev => 
                    prev > 0 ? prev - 1 : filteredOptions.length - 1
                );
                break;

            case 'Enter':
                e.preventDefault();
                if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
                    handleSelect(filteredOptions[highlightedIndex].id);
                }
                break;

            case 'Escape':
                e.preventDefault();
                setIsOpen(false);
                setSearchTerm(selectedOption?.name || '');
                setHighlightedIndex(-1);
                inputRef.current?.blur();
                break;

            case 'Tab':
                setIsOpen(false);
                setSearchTerm(selectedOption?.name || '');
                setHighlightedIndex(-1);
                break;

            default:
                break;
        }
    };

    const handleClear = () => {
        onChange('');
        setSearchTerm('');
        setIsOpen(false);
        setHighlightedIndex(-1);
        inputRef.current?.focus();
    };

    // Default input classes that match your design system
    const defaultInputClasses = "w-full h-11 px-4 py-2 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg shadow-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 transition-colors duration-200";
    const inputClasses = className || defaultInputClasses;

    return (
        <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
                {label}
            </label>
            <div className="relative" ref={wrapperRef}>
                <div className="relative">
                    <input
                        ref={inputRef}
                        type="text"
                        value={isOpen ? searchTerm : selectedOption?.name || ''}
                        onFocus={handleFocus}
                        onChange={handleInputChange}
                        onBlur={handleBlur}
                        onKeyDown={handleKeyDown}
                        placeholder={placeholder}
                        className={`${inputClasses} pr-20 cursor-pointer`}
                        autoComplete="off"
                        role="combobox"
                        aria-expanded={isOpen}
                        aria-haspopup="listbox"
                        aria-autocomplete="list"
                    />
                    
                    {/* Icons container */}
                    <div className="absolute inset-y-0 right-0 flex items-center">
                        {/* Clear button */}
                        {value && (
                            <button
                                type="button"
                                onClick={handleClear}
                                className="p-1 mr-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                                tabIndex={-1}
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        )}
                        
                        {/* Dropdown arrow */}
                        <div className="flex items-center pr-3 pointer-events-none">
                            <svg 
                                className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>
                </div>

                {/* Dropdown list */}
                {isOpen && (
                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-hidden">
                        <div 
                            ref={listRef}
                            className="max-h-60 overflow-y-auto py-1"
                            role="listbox"
                        >
                            {filteredOptions.length > 0 ? (
                                filteredOptions.map((option, index) => (
                                    <div
                                        key={option.id}
                                        onClick={() => handleSelect(option.id)}
                                        onMouseDown={(e) => e.preventDefault()}
                                        onMouseEnter={() => setHighlightedIndex(index)}
                                        className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                                            highlightedIndex === index
                                                ? 'bg-blue-50 text-blue-900'
                                                : 'text-gray-900 hover:bg-gray-50'
                                        }`}
                                        role="option"
                                        aria-selected={value === option.id}
                                    >
                                        <div className="flex items-center justify-between">
                                            <span className="font-medium">{option.name}</span>
                                            <span className="text-xs text-gray-500 ml-2">{option.id}</span>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="px-4 py-3 text-sm text-gray-500 text-center">
                                    No options found for {`"${searchTerm}"`}
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};