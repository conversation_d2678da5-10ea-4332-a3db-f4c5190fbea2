(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,16487,e=>{"use strict";e.s(["default",()=>x],16487);var s=e.i(74591);function t(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-slate-900 mb-1",children:"Executive Dashboard"}),(0,s.jsx)("p",{className:"text-slate-600 text-sm sm:text-base",children:"Comprehensive overview of your travel platform operations"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("button",{className:"px-3 py-2 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors text-sm font-medium whitespace-nowrap",children:[(0,s.jsx)("i",{className:"ri-download-line mr-2"}),"Export Data"]}),(0,s.jsxs)("button",{className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium whitespace-nowrap",children:[(0,s.jsx)("i",{className:"ri-add-line mr-2"}),"Quick Action"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-4 sm:p-6 text-white",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("p",{className:"text-blue-100 text-xs sm:text-sm font-medium",children:"Live Performance"}),(0,s.jsx)("p",{className:"text-xl sm:text-2xl font-bold truncate",children:"98.7%"}),(0,s.jsx)("p",{className:"text-blue-100 text-xs sm:text-sm",children:"System Uptime"})]}),(0,s.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-blue-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3",children:(0,s.jsx)("i",{className:"ri-pulse-line text-xl sm:text-2xl"})})]})}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-xl p-4 sm:p-6 text-white",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("p",{className:"text-emerald-100 text-xs sm:text-sm font-medium",children:"Active Sessions"}),(0,s.jsx)("p",{className:"text-xl sm:text-2xl font-bold truncate",children:"2,847"}),(0,s.jsx)("p",{className:"text-emerald-100 text-xs sm:text-sm",children:"Current Users"})]}),(0,s.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-emerald-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3",children:(0,s.jsx)("i",{className:"ri-user-line text-xl sm:text-2xl"})})]})}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl p-4 sm:p-6 text-white sm:col-span-2 lg:col-span-1",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("p",{className:"text-purple-100 text-xs sm:text-sm font-medium",children:"Today's Revenue"}),(0,s.jsx)("p",{className:"text-xl sm:text-2xl font-bold truncate",children:"$89.2K"}),(0,s.jsx)("p",{className:"text-purple-100 text-xs sm:text-sm",children:"+12.3% vs yesterday"})]}),(0,s.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-purple-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3",children:(0,s.jsx)("i",{className:"ri-trending-up-line text-xl sm:text-2xl"})})]})})]})]})}function l(){let e={blue:"from-blue-500 to-blue-600",emerald:"from-emerald-500 to-emerald-600",purple:"from-purple-500 to-purple-600",amber:"from-amber-500 to-amber-600",cyan:"from-cyan-500 to-cyan-600",rose:"from-rose-500 to-rose-600"},t={blue:"bg-blue-50 border-blue-100",emerald:"bg-emerald-50 border-emerald-100",purple:"bg-purple-50 border-purple-100",amber:"bg-amber-50 border-amber-100",cyan:"bg-cyan-50 border-cyan-100",rose:"bg-rose-50 border-rose-100"};return(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[{title:"Total Bookings",value:"47,892",change:"+18.2%",changeType:"positive",icon:"ri-calendar-check-fill",color:"blue",trend:"up",subtitle:"This month vs last month",additionalInfo:"2,847 today"},{title:"Gross Revenue",value:"$8.7M",change:"+24.5%",changeType:"positive",icon:"ri-money-dollar-circle-fill",color:"emerald",trend:"up",subtitle:"Monthly recurring revenue",additionalInfo:"$289K today"},{title:"Active Properties",value:"2,847",change:"+12.3%",changeType:"positive",icon:"ri-building-2-fill",color:"purple",trend:"up",subtitle:"Verified hotel partners",additionalInfo:"47 new this week"},{title:"Customer Satisfaction",value:"4.89",change:"+0.12",changeType:"positive",icon:"ri-star-fill",color:"amber",trend:"up",subtitle:"Average rating score",additionalInfo:"12,847 reviews"},{title:"Support Resolution",value:"97.2%",change:"****%",changeType:"positive",icon:"ri-customer-service-2-fill",color:"cyan",trend:"up",subtitle:"First contact resolution",additionalInfo:"Avg 4.2 min response"},{title:"Conversion Rate",value:"12.8%",change:"****%",changeType:"positive",icon:"ri-arrow-up-circle-fill",color:"rose",trend:"up",subtitle:"Visitor to booking ratio",additionalInfo:"Above industry avg"}].map((l,r)=>(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow ".concat(t[l.color]," overflow-hidden"),children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3 sm:mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br ".concat(e[l.color]," rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0"),children:(0,s.jsx)("i",{className:"".concat(l.icon," text-white text-lg sm:text-xl")})}),(0,s.jsx)("div",{className:"text-right ml-2",children:(0,s.jsxs)("div",{className:"inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ".concat("positive"===l.changeType?"bg-emerald-100 text-emerald-700":"bg-red-100 text-red-700"," whitespace-nowrap"),children:[(0,s.jsx)("i",{className:"".concat("positive"===l.changeType?"ri-arrow-up-line":"ri-arrow-down-line"," mr-1")}),l.change]})})]}),(0,s.jsxs)("div",{className:"mb-3 min-w-0",children:[(0,s.jsx)("h3",{className:"text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate",children:l.value}),(0,s.jsx)("p",{className:"text-sm font-medium text-slate-600 truncate",children:l.title})]}),(0,s.jsxs)("div",{className:"space-y-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-xs text-slate-500 truncate",children:l.subtitle}),(0,s.jsx)("p",{className:"text-xs text-slate-400 truncate",children:l.additionalInfo})]}),(0,s.jsx)("div",{className:"mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-slate-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-xs text-slate-500",children:"Trend"}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-emerald-400 rounded-full"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-emerald-300 rounded-full"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-slate-200 rounded-full"})]})]})})]},r))})}var r=e.i(94528);function i(){let[e,t]=(0,r.useState)("7D");return(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"min-w-0",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-slate-900 truncate",children:"Revenue Analytics"}),(0,s.jsx)("p",{className:"text-slate-600 text-sm",children:"Detailed breakdown of revenue streams"})]}),(0,s.jsx)("div",{className:"flex items-center gap-2 overflow-x-auto",children:["7D","30D","90D","1Y"].map(l=>(0,s.jsx)("button",{onClick:()=>t(l),className:"px-3 py-2 text-sm font-medium rounded-lg transition-colors whitespace-nowrap ".concat(e===l?"bg-blue-600 text-white":"text-slate-600 hover:bg-slate-100"),children:l},l))})]}),(0,s.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6",children:[{label:"Total Revenue",value:"$2.4M",change:"+18.2%",positive:!0},{label:"Hotel Bookings",value:"$1.8M",change:"+15.7%",positive:!0},{label:"Service Fees",value:"$420K",change:"+22.1%",positive:!0},{label:"Other Revenue",value:"$180K",change:"****%",positive:!0}].map((e,t)=>(0,s.jsxs)("div",{className:"text-center min-w-0",children:[(0,s.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-slate-900 truncate",children:e.value}),(0,s.jsx)("p",{className:"text-xs sm:text-sm text-slate-600 truncate",children:e.label}),(0,s.jsx)("p",{className:"text-xs sm:text-sm font-medium ".concat(e.positive?"text-emerald-600":"text-red-600"),children:e.change})]},t))}),(0,s.jsx)("div",{className:"h-64 sm:h-80 bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl flex items-center justify-center border border-slate-100",children:(0,s.jsxs)("div",{className:"text-center p-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("i",{className:"ri-line-chart-line text-xl sm:text-2xl text-blue-600"})}),(0,s.jsx)("p",{className:"text-slate-600 font-medium text-sm sm:text-base",children:"Advanced Revenue Chart"}),(0,s.jsx)("p",{className:"text-xs sm:text-sm text-slate-500 mt-2",children:"Interactive analytics with real-time data visualization"})]})})]})}function a(){return(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-slate-900",children:"Top Performing Properties"}),(0,s.jsx)("button",{className:"text-blue-600 hover:text-blue-700 text-sm font-medium whitespace-nowrap",children:"View All Properties"})]}),(0,s.jsx)("div",{className:"space-y-3 sm:space-y-4",children:[{name:"The Ritz-Carlton Downtown",bookings:247,revenue:"$189,420",rating:4.9,growth:"+28%"},{name:"Marriott Executive Suites",bookings:198,revenue:"$145,830",rating:4.8,growth:"+22%"},{name:"Hilton Garden Inn Premium",bookings:185,revenue:"$128,290",rating:4.7,growth:"+18%"},{name:"Courtyard Business Center",bookings:162,revenue:"$98,160",rating:4.6,growth:"+15%"},{name:"Holiday Inn Express Plus",bookings:134,revenue:"$87,280",rating:4.5,growth:"+12%"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 sm:p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1",children:[(0,s.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("i",{className:"ri-building-2-line text-white text-lg sm:text-xl"})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-slate-900 text-sm sm:text-base truncate",children:e.name}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 mt-1",children:[(0,s.jsxs)("p",{className:"text-xs sm:text-sm text-slate-600 whitespace-nowrap",children:[e.bookings," bookings"]}),(0,s.jsxs)("div",{className:"flex items-center whitespace-nowrap",children:[(0,s.jsx)("i",{className:"ri-star-fill text-amber-400 text-xs sm:text-sm mr-1"}),(0,s.jsx)("span",{className:"text-xs sm:text-sm text-slate-600",children:e.rating})]})]})]})]}),(0,s.jsxs)("div",{className:"text-right ml-3 flex-shrink-0",children:[(0,s.jsx)("p",{className:"font-bold text-slate-900 text-sm sm:text-lg",children:e.revenue}),(0,s.jsx)("p",{className:"text-emerald-600 text-xs sm:text-sm font-medium",children:e.growth})]})]},t))})]})}function n(){let[e,t]=(0,r.useState)("operations"),l={blue:"bg-blue-50 border-blue-200 hover:bg-blue-100 hover:border-blue-300",emerald:"bg-emerald-50 border-emerald-200 hover:bg-emerald-100 hover:border-emerald-300",amber:"bg-amber-50 border-amber-200 hover:bg-amber-100 hover:border-amber-300",purple:"bg-purple-50 border-purple-200 hover:bg-purple-100 hover:border-purple-300",rose:"bg-rose-50 border-rose-200 hover:bg-rose-100 hover:border-rose-300",indigo:"bg-indigo-50 border-indigo-200 hover:bg-indigo-100 hover:border-indigo-300",cyan:"bg-cyan-50 border-cyan-200 hover:bg-cyan-100 hover:border-cyan-300",teal:"bg-teal-50 border-teal-200 hover:bg-teal-100 hover:border-teal-300",green:"bg-green-50 border-green-200 hover:bg-green-100 hover:border-green-300"},i={blue:"text-blue-600",emerald:"text-emerald-600",amber:"text-amber-600",purple:"text-purple-600",rose:"text-rose-600",indigo:"text-indigo-600",cyan:"text-cyan-600",teal:"text-teal-600",green:"text-green-600"},a={high:"bg-red-500",medium:"bg-amber-500",low:"bg-slate-400"};return(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"min-w-0",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-slate-900",children:"Quick Actions"}),(0,s.jsx)("p",{className:"text-sm text-slate-500",children:"Streamlined access to key operations"})]}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-slate-100 rounded-xl p-1 overflow-x-auto",children:[{id:"operations",label:"Operations",icon:"ri-settings-line"},{id:"marketing",label:"Marketing",icon:"ri-megaphone-line"},{id:"finance",label:"Finance",icon:"ri-bar-chart-line"}].map(l=>(0,s.jsxs)("button",{onClick:()=>t(l.id),className:"px-3 py-2 rounded-lg text-sm font-medium transition-all whitespace-nowrap ".concat(e===l.id?"bg-white text-slate-900 shadow-sm":"text-slate-600 hover:text-slate-900"),children:[(0,s.jsx)("i",{className:"".concat(l.icon," mr-2")}),l.label]},l.id))})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-3 sm:gap-4 mb-6",children:({operations:[{title:"Add Premium Property",description:"Register luxury hotel partner",icon:"ri-building-2-line",color:"blue",action:()=>console.log("Add premium property"),priority:"high"},{title:"Bulk Inventory Update",description:"Update room availability across properties",icon:"ri-database-2-line",color:"emerald",action:()=>console.log("Bulk inventory update"),priority:"medium"},{title:"Emergency Maintenance",description:"Schedule system maintenance window",icon:"ri-tools-line",color:"amber",action:()=>console.log("Emergency maintenance"),priority:"high"}],marketing:[{title:"Launch Campaign",description:"Create targeted marketing campaign",icon:"ri-megaphone-line",color:"purple",action:()=>console.log("Launch campaign"),priority:"medium"},{title:"Seasonal Promotions",description:"Set up holiday discount packages",icon:"ri-gift-line",color:"rose",action:()=>console.log("Seasonal promotions"),priority:"medium"},{title:"Loyalty Program",description:"Manage premium member benefits",icon:"ri-vip-crown-line",color:"indigo",action:()=>console.log("Loyalty program"),priority:"low"}],finance:[{title:"Revenue Report",description:"Generate comprehensive financial analysis",icon:"ri-bar-chart-box-line",color:"cyan",action:()=>console.log("Revenue report"),priority:"high"},{title:"Payment Gateway",description:"Configure new payment methods",icon:"ri-bank-card-line",color:"teal",action:()=>console.log("Payment gateway"),priority:"medium"},{title:"Expense Tracking",description:"Monitor operational costs",icon:"ri-money-dollar-circle-line",color:"green",action:()=>console.log("Expense tracking"),priority:"low"}]})[e].map((e,t)=>(0,s.jsx)("div",{className:"p-4 sm:p-5 rounded-xl border-2 transition-all cursor-pointer group ".concat(l[e.color]," overflow-hidden"),onClick:e.action,children:(0,s.jsxs)("div",{className:"flex items-center justify-between gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1",children:[(0,s.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-xl bg-white shadow-sm ".concat(i[e.color]," flex-shrink-0"),children:(0,s.jsx)("i",{className:"".concat(e.icon," text-lg sm:text-xl")})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-slate-900 text-sm sm:text-base truncate",children:e.title}),(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(a[e.priority]," flex-shrink-0")})]}),(0,s.jsx)("p",{className:"text-sm text-slate-600 line-clamp-2",children:e.description})]})]}),(0,s.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0",children:(0,s.jsx)("i",{className:"ri-arrow-right-line text-slate-400"})})]})},t))}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,s.jsxs)("button",{className:"w-full flex items-center justify-center py-2 sm:py-3 px-4 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors text-sm font-medium",children:[(0,s.jsx)("i",{className:"ri-add-line mr-2"}),"Custom Action"]}),(0,s.jsxs)("button",{className:"w-full flex items-center justify-center py-2 sm:py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium",children:[(0,s.jsx)("i",{className:"ri-dashboard-line mr-2"}),"Action Center"]})]}),(0,s.jsx)("div",{className:"pt-3 sm:pt-4 border-t border-slate-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-slate-500",children:"Frequently Used"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,s.jsx)("i",{className:"ri-hotel-line text-blue-600 text-sm"})}),(0,s.jsx)("div",{className:"w-6 h-6 bg-emerald-100 rounded-lg flex items-center justify-center",children:(0,s.jsx)("i",{className:"ri-user-add-line text-emerald-600 text-sm"})}),(0,s.jsx)("div",{className:"w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,s.jsx)("i",{className:"ri-file-download-line text-purple-600 text-sm"})})]})]})})]})]})}function o(){let e={emerald:"bg-emerald-100 text-emerald-700 border-emerald-200",blue:"bg-blue-100 text-blue-700 border-blue-200",purple:"bg-purple-100 text-purple-700 border-purple-200",amber:"bg-amber-100 text-amber-700 border-amber-200",cyan:"bg-cyan-100 text-cyan-700 border-cyan-200",rose:"bg-rose-100 text-rose-700 border-rose-200"},t={high:"bg-red-500",medium:"bg-amber-500",low:"bg-slate-400"};return(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"min-w-0",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-slate-900",children:"Live Activity Feed"}),(0,s.jsx)("p",{className:"text-sm text-slate-500",children:"Real-time system and business events"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-sm text-slate-600",children:"Live"})]})]}),(0,s.jsx)("div",{className:"space-y-3 sm:space-y-4 max-h-80 sm:max-h-96 overflow-y-auto scrollbar-thin",children:[{id:1,type:"booking",title:"High-Value Booking Created",message:"Premium suite booking at The Ritz-Carlton",user:"Sarah Johnson",time:"2 minutes ago",icon:"ri-vip-crown-line",color:"emerald",amount:"$4,250",priority:"high"},{id:2,type:"payment",title:"Payment Processing Alert",message:"Large transaction processed successfully",user:"Payment System",time:"5 minutes ago",icon:"ri-secure-payment-line",color:"blue",amount:"$12,850",priority:"medium"},{id:3,type:"support",title:"Escalated Ticket Resolved",message:"VIP customer issue resolved - 5-star rating",user:"Mike Chen",time:"12 minutes ago",icon:"ri-customer-service-2-line",color:"purple",amount:null,priority:"high"},{id:4,type:"hotel",title:"Property Partnership",message:"New luxury hotel added to portfolio",user:"Partnership Team",time:"25 minutes ago",icon:"ri-building-2-line",color:"amber",amount:null,priority:"medium"},{id:5,type:"system",title:"Performance Optimization",message:"API response time improved by 23%",user:"Engineering Team",time:"1 hour ago",icon:"ri-speed-up-line",color:"cyan",amount:null,priority:"low"},{id:6,type:"user",title:"Premium User Registration",message:"Corporate account created with $50K credit",user:"Sales Team",time:"2 hours ago",icon:"ri-user-star-line",color:"rose",amount:"$50,000",priority:"high"}].map(l=>(0,s.jsx)("div",{className:"p-3 sm:p-4 rounded-xl border-2 transition-all hover:shadow-sm ".concat(e[l.color]," overflow-hidden"),children:(0,s.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3 min-w-0 flex-1",children:[(0,s.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center border-2 ".concat(e[l.color]," flex-shrink-0"),children:(0,s.jsx)("i",{className:"".concat(l.icon," text-base sm:text-lg")})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("p",{className:"font-semibold text-slate-900 text-sm truncate",children:l.title}),(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t[l.priority]," flex-shrink-0")})]}),(0,s.jsx)("p",{className:"text-sm text-slate-600 mb-2 line-clamp-2",children:l.message}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 text-xs text-slate-500 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1 min-w-0",children:[(0,s.jsx)("i",{className:"ri-user-line flex-shrink-0"}),(0,s.jsx)("span",{className:"truncate",children:l.user})]}),(0,s.jsx)("span",{className:"flex-shrink-0",children:"•"}),(0,s.jsxs)("div",{className:"flex items-center space-x-1 whitespace-nowrap",children:[(0,s.jsx)("i",{className:"ri-time-line"}),(0,s.jsx)("span",{children:l.time})]})]})]})]}),(0,s.jsxs)("div",{className:"text-right flex-shrink-0",children:[l.amount&&(0,s.jsx)("div",{className:"font-bold text-slate-900 text-sm mb-1",children:l.amount}),(0,s.jsx)("button",{className:"text-xs text-slate-500 hover:text-slate-700 transition-colors whitespace-nowrap",children:"View Details"})]})]})},l.id))}),(0,s.jsx)("div",{className:"mt-4 sm:mt-6 pt-4 border-t border-slate-200",children:(0,s.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 py-2 sm:py-3 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-xl transition-colors",children:[(0,s.jsx)("i",{className:"ri-refresh-line"}),(0,s.jsx)("span",{children:"Load More Activities"})]})})]})}function c(){return(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,s.jsx)("h3",{className:"font-semibold text-slate-900 text-lg",children:"System Health Monitor"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-emerald-500 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-sm text-emerald-600 font-medium",children:"Optimal"})]})]}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"API Response Time",value:"89ms",percentage:75,color:"emerald"},{label:"Database Performance",value:"99.2%",percentage:92,color:"emerald"},{label:"Payment Gateway",value:"Online",percentage:100,color:"emerald",isStatus:!0},{label:"CDN Performance",value:"98.7%",percentage:87,color:"emerald"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex justify-between items-center gap-3",children:[(0,s.jsx)("span",{className:"text-sm text-slate-600 min-w-0 flex-1 truncate",children:e.label}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,s.jsx)("span",{className:"text-sm font-semibold text-emerald-600 whitespace-nowrap",children:e.value}),e.isStatus?(0,s.jsx)("div",{className:"w-3 h-3 bg-emerald-500 rounded-full"}):(0,s.jsx)("div",{className:"w-12 h-2 bg-slate-200 rounded-full",children:(0,s.jsx)("div",{className:"h-2 bg-emerald-500 rounded-full transition-all duration-300",style:{width:"".concat(e.percentage,"%")}})})]})]},t))})]})}function m(){return(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,s.jsx)("h3",{className:"font-semibold text-slate-900 mb-6 text-lg",children:"Revenue Distribution"}),(0,s.jsx)("div",{className:"space-y-4",children:[{source:"Hotel Commissions",amount:"$1,847,290",percentage:74,color:"bg-blue-500"},{source:"Service Fees",amount:"$386,470",percentage:16,color:"bg-emerald-500"},{source:"Premium Features",amount:"$189,340",percentage:8,color:"bg-purple-500"},{source:"Partnership Revenue",amount:"$89,900",percentage:2,color:"bg-amber-500"}].map((e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2 gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 min-w-0 flex-1",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.color," flex-shrink-0")}),(0,s.jsx)("span",{className:"text-sm text-slate-600 truncate",children:e.source})]}),(0,s.jsx)("span",{className:"text-sm font-semibold text-slate-900 whitespace-nowrap",children:e.amount})]}),(0,s.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full ".concat(e.color," transition-all duration-500"),style:{width:"".concat(e.percentage,"%")}})})]},t))})]})}function d(){return(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,s.jsx)("h3",{className:"font-semibold text-slate-900 text-lg",children:"Priority Tasks"}),(0,s.jsx)("button",{className:"text-blue-600 hover:text-blue-700 text-sm font-medium whitespace-nowrap",children:"Manage All"})]}),(0,s.jsx)("div",{className:"space-y-3 sm:space-y-4",children:[{title:"Critical: Payment Disputes",description:"5 high-value disputes require immediate attention",dueTime:"Due in 1 hour",priority:"high",bgColor:"bg-red-50",borderColor:"border-red-200",dotColor:"bg-red-500",textColor:"text-red-600"},{title:"Hotel Inventory Sync",description:"Update 47 properties with new availability",dueTime:"Due today",priority:"medium",bgColor:"bg-amber-50",borderColor:"border-amber-200",dotColor:"bg-amber-500",textColor:"text-amber-600"},{title:"New Partner Approvals",description:"Review and approve 8 new hotel partnerships",dueTime:"Due tomorrow",priority:"low",bgColor:"bg-blue-50",borderColor:"border-blue-200",dotColor:"bg-blue-500",textColor:"text-blue-600"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.bgColor," ").concat(e.borderColor," overflow-hidden"),children:[(0,s.jsx)("div",{className:"w-2 h-2 ".concat(e.dotColor," rounded-full mt-2 flex-shrink-0")}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-slate-900 truncate",children:e.title}),(0,s.jsx)("p",{className:"text-xs text-slate-600 mb-2 line-clamp-2",children:e.description}),(0,s.jsx)("p",{className:"text-xs font-medium ".concat(e.textColor),children:e.dueTime})]})]},t))})]})}function x(){return(0,s.jsx)("main",{className:"min-h-screen bg-slate-50 p-4 sm:p-6 lg:p-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,s.jsx)(t,{}),(0,s.jsx)(l,{}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsx)(i,{}),(0,s.jsx)(a,{})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(o,{}),(0,s.jsx)(n,{})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,s.jsx)(c,{}),(0,s.jsx)(m,{}),(0,s.jsx)(d,{})]})]})})}}]);