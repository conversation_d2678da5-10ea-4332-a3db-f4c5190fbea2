var R=require("../../chunks/ssr/[turbopack]_runtime.js")("server/app/_not-found/page.js")
R.c("server/chunks/ssr/3c249_84c36321._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/b2c-flight_app_8923d9f8._.js")
R.c("server/chunks/ssr/[root-of-the-server]__82d0622e._.js")
R.c("server/chunks/ssr/3c249_next_dist_client_components_751e8ecb._.js")
R.c("server/chunks/ssr/3c249_next_dist_client_components_builtin_forbidden_5a2d37e7.js")
R.c("server/chunks/ssr/3c249_next_dist_87319aad._.js")
R.c("server/chunks/ssr/[root-of-the-server]__69dccdc2._.js")
R.m("[project]/b2c-flight/.next-internal/server/app/_not-found/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/b2c-flight/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { METADATA_0 => \"[project]/b2c-flight/app/favicon.ico.mjs { IMAGE => \\\"[project]/b2c-flight/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/b2c-flight/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/b2c-flight/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/b2c-flight/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/b2c-flight/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/b2c-flight/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/b2c-flight/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { METADATA_0 => \"[project]/b2c-flight/app/favicon.ico.mjs { IMAGE => \\\"[project]/b2c-flight/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/b2c-flight/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/b2c-flight/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/b2c-flight/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/b2c-flight/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/b2c-flight/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
