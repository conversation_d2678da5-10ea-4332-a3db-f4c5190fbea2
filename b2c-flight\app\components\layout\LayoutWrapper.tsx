'use client';
import React, { useState, useEffect } from 'react';
import TopNavigation from '../header/TopNavigation';
import Sidebar from '../sidebar/Sidebar';

interface LayoutWrapperProps {
  children: React.ReactNode;
}

export default function LayoutWrapper({ children }: LayoutWrapperProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile) {
        setSidebarCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Get sidebar width based on state
  const getSidebarWidth = () => {
    if (isMobile) {
      return sidebarCollapsed ? 'w-0' : 'w-64';
    }
    return sidebarCollapsed ? 'w-16' : 'w-64';
  };

  return (
    <div className="h-screen w-full bg-slate-50 flex flex-col overflow-hidden">
      {/* Header - Fixed height of 64px (h-16) */}
      <header className="w-full h-16 flex-shrink-0 z-40 bg-white border-b">
        <TopNavigation />
      </header>

      {/* Main Container - Exact remaining height (100vh - 64px) */}
      <div className="flex w-full h-[calc(100vh-4rem)]">
        {/* Sidebar - Fixed Width, Full Height */}
        <aside 
          className={`${getSidebarWidth()} flex-shrink-0 transition-all duration-300 ease-in-out relative z-30 h-full ${
            isMobile && sidebarCollapsed ? 'overflow-hidden' : ''
          }`}
        >
          <div className="h-full">
            <Sidebar
              isCollapsed={sidebarCollapsed}
              onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
              isMobile={isMobile}
            />
          </div>
        </aside>

        {/* Content Area - Remaining Width, Full Height */}
        <main className="flex-1 overflow-y-auto overflow-x-hidden custom-scrollbar bg-white h-full">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>

      {/* Mobile Overlay */}
      {isMobile && !sidebarCollapsed && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20"
          onClick={() => setSidebarCollapsed(true)}
        />
      )}
    </div>
  );
}