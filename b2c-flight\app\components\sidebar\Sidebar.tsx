
'use client';

import React, { useState } from 'react';
import Link from 'next/link';

interface SidebarProps {
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  isMobile?: boolean;
}

export default function Sidebar({
  isCollapsed = false,
  onToggleCollapse,
  isMobile = false
}: SidebarProps) {
  const [activeItem, setActiveItem] = useState('Dashboard');

  const menuItems = [
    {
      name: 'Dashboard',
      icon: 'ri-dashboard-line',
      href: '/',
      active: true,
    },
    {
      name: 'Airport',
      icon: 'ri-hotel-line',
      href: '/airport-master',
      active: false,
    },
    {
      name: 'Airlines',
      icon: 'ri-calendar-check-line',
      href: '/airlines',
      active: false,
    },
    {
      name: 'Customers',
      icon: 'ri-building-2-line',
      href: '/customers',
      active: false,
    },
    {
      name: 'Bookings',
      icon: 'ri-price-tag-3-line',
      href: '/bookings',
      active: false,
    },
  //   {
  //     name: 'Payments',
  //     icon: 'ri-bank-card-line',
  //     href: '/payments',
  //     active: false,
  //   },
  //   {
  //     name: 'User Management',
  //     icon: 'ri-user-settings-line',
  //     href: '/users',
  //     active: false,
  //   },
  //   {
  //     name: 'Promotions',
  //     icon: 'ri-discount-percent-line',
  //     href: '/promotions',
  //     active: false,
  //   },
  //   {
  //     name: 'Support Tickets',
  //     icon: 'ri-customer-service-2-line',
  //     href: '/support',
  //     active: false,
  //   },
  //   {
  //     name: 'Content Management',
  //     icon: 'ri-file-text-line',
  //     href: '/content',
  //     active: false,
  //   },
  //   {
  //     name: 'Reports',
  //     icon: 'ri-bar-chart-line',
  //     href: '/reports',
  //     active: false,
  //   },
  //   {
  //     name: 'System Settings',
  //     icon: 'ri-settings-3-line',
  //     href: '/settings',
  //     active: false,
  //   },
  // ];

  // const futureModules = [
  //   {
  //     name: 'Flight Management',
  //     icon: 'ri-flight-takeoff-line',
  //     href: '/flights',
  //     disabled: true,
  //   },
  //   {
  //     name: 'Cruise Management',
  //     icon: 'ri-ship-line',
  //     href: '/cruises',
  //     disabled: true,
  //   },
  //   {
  //     name: 'Holiday Packages',
  //     icon: 'ri-suitcase-line',
  //     href: '/packages',
  //     disabled: true,
  //   },
  ];

  return (
    <div
      className={`sidebar h-full transition-all duration-300 ${
        isCollapsed ? 'w-16' : 'w-64'
      } flex flex-col shadow-xl flex-shrink-0 overflow-hidden`}
      style={{
        backgroundColor: 'var(--color-sidebar-bg)',
        color: 'var(--color-sidebar-text)',
        borderRight: '1px solid var(--color-sidebar-border)'
      }}
    >
      {/* Responsive Header */}
      <div 
        className="p-4 flex-shrink-0"
        style={{ borderBottom: '1px solid var(--color-sidebar-border)' }}
      >
        <button
          onClick={onToggleCollapse}
          className="w-full flex items-center justify-between rounded-lg p-2 transition-colors touch-target hover:bg-opacity-10"
          style={{
            color: 'var(--color-sidebar-text)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(203, 213, 225, 0.1)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          {!isCollapsed && (
            <div>
              <span className="font-semibold text-responsive-sm" style={{ color: 'var(--color-sidebar-text)' }}>
                Navigation Menu
              </span>
              <p className="text-xs mt-0.5 opacity-75">Enterprise Dashboard</p>
            </div>
          )}
          <i
            className={`ri-menu-fold-line opacity-75 ${
              isCollapsed ? 'rotate-180' : ''
            } transition-transform text-lg`}
          ></i>
        </button>
      </div>

      {/* Responsive Menu Sections */}
      <div className="flex-1 sidebar-scroll-container">
        <div className="overflow-y-auto py-2 sidebar-scrollbar h-full">
          <div className="px-2 space-y-1">
          {menuItems.map((item) => (
            <Link key={item.name} href={item.href}>
              <div
                className={`sidebar-item flex items-center px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 touch-target ${
                  activeItem === item.name ? 'active' : ''
                }`}
                onClick={() => setActiveItem(item.name)}
                style={{
                  backgroundColor: activeItem === item.name ? 'var(--color-primary-600)' : 'transparent',
                  color: activeItem === item.name ? 'var(--color-sidebar-text-active)' : 'var(--color-sidebar-text)'
                }}
                onMouseEnter={(e) => {
                  if (activeItem !== item.name) {
                    e.currentTarget.style.backgroundColor = 'rgba(203, 213, 225, 0.1)';
                    e.currentTarget.style.color = 'var(--color-sidebar-text-active)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeItem !== item.name) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--color-sidebar-text)';
                  }
                }}
              >
                <div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
                  <i className={`${item.icon} text-base`}></i>
                </div>
                {!isCollapsed && (
                  <span className="text-responsive-sm font-medium ml-3 truncate">
                    {item.name}
                  </span>
                )}
                {!isCollapsed && isMobile && (
                  <i className="ri-arrow-right-s-line ml-auto opacity-50"></i>
                )}
              </div>
            </Link>
          ))}
        </div>

          {/* Responsive Future Verticals */}
          {!isCollapsed && (
            <div className="px-4 mt-6">
              <div className="flex items-center justify-between mb-3">
                <div className="text-xs font-semibold uppercase tracking-wider opacity-75">
                  Future Modules
                </div>
                <div
                  className="px-2 py-0.5 rounded-full"
                  style={{ backgroundColor: 'rgba(203, 213, 225, 0.2)' }}
                >
                  <span className="text-xs opacity-75">Soon</span>
                </div>
              </div>
              <div className="space-y-1">
                {futureModules.map((item) => (
                  <div
                    key={item.name}
                    className="flex items-center px-3 py-2.5 rounded-lg cursor-not-allowed opacity-50 touch-target"
                  >
                    <div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
                      <i className={`${item.icon} text-base`}></i>
                    </div>
                    <span className="text-responsive-sm font-medium ml-3 truncate">{item.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Responsive System Status Footer */}
      <div 
        className="p-4 flex-shrink-0"
        style={{ borderTop: '1px solid var(--color-sidebar-border)' }}
      >
        <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>
          <div 
            className="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0"
            style={{ backgroundColor: 'var(--color-success-600)' }}
          >
            <i className="ri-shield-check-line text-white text-sm"></i>
          </div>
          {!isCollapsed && (
            <div className="min-w-0">
              <p className="text-responsive-sm font-medium truncate" style={{ color: 'var(--color-sidebar-text)' }}>
                System Status
              </p>
              <div className="flex items-center space-x-2 mt-0.5">
                <div 
                  className="w-2 h-2 rounded-full animate-pulse flex-shrink-0"
                  style={{ backgroundColor: 'var(--color-success-400)' }}
                ></div>
                <p className="text-xs truncate" style={{ color: 'var(--color-success-400)' }}>
                  All Systems Online
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
