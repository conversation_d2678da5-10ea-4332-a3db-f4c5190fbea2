[{"C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\api\\api-service.ts": "1", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\api\\axiosInstance.ts": "2", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\header\\TopNavigation.tsx": "3", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\analytics\\RevenueAnalytics.tsx": "4", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\dashboard-status\\DashboardStats.tsx": "5", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\header\\DashboardHeader.tsx": "6", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\monitoring\\SystemHealth.tsx": "7", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\properties\\TopProperties.tsx": "8", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\quick-actions\\QuickActions.tsx": "9", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\recent-activity\\RecentActivity.tsx": "10", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\revenue\\RevenueDistribution.tsx": "11", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\tasks\\PriorityTasks.tsx": "12", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\layout\\LayoutWrapper.tsx": "13", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\search-select-input\\SearchSelectInput.tsx": "14", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\sidebar\\Sidebar.tsx": "15", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\Button.tsx": "16", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\HeaderCards.tsx": "17", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\Modal.tsx": "18", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\ModalWithTabs.tsx": "19", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\PageSectionHeader.tsx": "20", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\TabbedModal.tsx": "21", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\layout.tsx": "22", "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\page.tsx": "23"}, {"size": 2794, "mtime": 1759737249433, "results": "24", "hashOfConfig": "25"}, {"size": 1673, "mtime": 1759737261814, "results": "26", "hashOfConfig": "25"}, {"size": 17146, "mtime": 1759745609579, "results": "27", "hashOfConfig": "25"}, {"size": 2982, "mtime": 1759733565754, "results": "28", "hashOfConfig": "25"}, {"size": 5252, "mtime": 1759733565755, "results": "29", "hashOfConfig": "25"}, {"size": 3516, "mtime": 1759743877518, "results": "30", "hashOfConfig": "25"}, {"size": 2048, "mtime": 1759733565756, "results": "31", "hashOfConfig": "25"}, {"size": 3037, "mtime": 1759733565757, "results": "32", "hashOfConfig": "25"}, {"size": 9174, "mtime": 1759743692926, "results": "33", "hashOfConfig": "25"}, {"size": 6113, "mtime": 1759733565758, "results": "34", "hashOfConfig": "25"}, {"size": 1658, "mtime": 1759733565759, "results": "35", "hashOfConfig": "25"}, {"size": 2305, "mtime": 1759733565760, "results": "36", "hashOfConfig": "25"}, {"size": 2481, "mtime": 1759745574111, "results": "37", "hashOfConfig": "25"}, {"size": 10184, "mtime": 1759743915411, "results": "38", "hashOfConfig": "25"}, {"size": 8796, "mtime": 1759745447238, "results": "39", "hashOfConfig": "25"}, {"size": 1180, "mtime": 1751951319903, "results": "40", "hashOfConfig": "25"}, {"size": 4550, "mtime": 1759733565830, "results": "41", "hashOfConfig": "25"}, {"size": 4551, "mtime": 1751951319903, "results": "42", "hashOfConfig": "25"}, {"size": 5068, "mtime": 1759733565846, "results": "43", "hashOfConfig": "25"}, {"size": 1824, "mtime": 1759733565861, "results": "44", "hashOfConfig": "25"}, {"size": 6756, "mtime": 1751951319903, "results": "45", "hashOfConfig": "25"}, {"size": 1091, "mtime": 1759745291102, "results": "46", "hashOfConfig": "25"}, {"size": 1801, "mtime": 1759745321847, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rptgm4", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\api\\api-service.ts", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\api\\axiosInstance.ts", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\header\\TopNavigation.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\analytics\\RevenueAnalytics.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\dashboard-status\\DashboardStats.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\header\\DashboardHeader.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\monitoring\\SystemHealth.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\properties\\TopProperties.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\quick-actions\\QuickActions.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\recent-activity\\RecentActivity.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\revenue\\RevenueDistribution.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\home\\tasks\\PriorityTasks.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\layout\\LayoutWrapper.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\search-select-input\\SearchSelectInput.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\sidebar\\Sidebar.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\Button.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\HeaderCards.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\Modal.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\ModalWithTabs.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\PageSectionHeader.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\components\\ui\\TabbedModal.tsx", [], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\layout.tsx", ["117"], [], "C:\\Ecogo\\B2C-dashboard\\b2c-flight\\app\\page.tsx", [], [], {"ruleId": "118", "severity": 1, "message": "119", "line": 32, "column": 9, "nodeType": "120", "endLine": 35, "endColumn": 11}, "@next/next/no-page-custom-font", "Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font", "JSXOpeningElement"]