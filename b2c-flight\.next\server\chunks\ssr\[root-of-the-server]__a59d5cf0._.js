module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},77491,(a,b,c)=>{},19718,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(39110).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/b2c-flight/app/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/b2c-flight/app/page.tsx <module evaluation>","default")},13293,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(39110).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/b2c-flight/app/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/b2c-flight/app/page.tsx","default")},82236,a=>{"use strict";a.i(19718);var b=a.i(13293);a.n(b)}];

//# sourceMappingURL=%5Broot-of-the-server%5D__a59d5cf0._.js.map