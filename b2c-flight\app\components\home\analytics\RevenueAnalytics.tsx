'use client';

import React, { useState } from 'react';

export default function RevenueAnalytics() {
  const [activeTimeframe, setActiveTimeframe] = useState('7D');

  const timeframes = ['7D', '30D', '90D', '1Y'];

  const revenueStats = [
    { label: 'Total Revenue', value: '$2.4M', change: '+18.2%', positive: true },
    { label: 'Hotel Bookings', value: '$1.8M', change: '+15.7%', positive: true },
    { label: 'Service Fees', value: '$420K', change: '+22.1%', positive: true },
    { label: 'Other Revenue', value: '$180K', change: '****%', positive: true }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 overflow-hidden">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div className="min-w-0">
          <h2 className="text-lg sm:text-xl font-semibold text-slate-900 truncate">Revenue Analytics</h2>
          <p className="text-slate-600 text-sm">Detailed breakdown of revenue streams</p>
        </div>
        <div className="flex items-center gap-2 overflow-x-auto">
          {timeframes.map((timeframe) => (
            <button
              key={timeframe}
              onClick={() => setActiveTimeframe(timeframe)}
              className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors whitespace-nowrap ${
                activeTimeframe === timeframe
                  ? 'bg-blue-600 text-white'
                  : 'text-slate-600 hover:bg-slate-100'
              }`}
            >
              {timeframe}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6">
        {revenueStats.map((stat, index) => (
          <div key={index} className="text-center min-w-0">
            <p className="text-xl sm:text-2xl font-bold text-slate-900 truncate">{stat.value}</p>
            <p className="text-xs sm:text-sm text-slate-600 truncate">{stat.label}</p>
            <p className={`text-xs sm:text-sm font-medium ${
              stat.positive ? 'text-emerald-600' : 'text-red-600'
            }`}>
              {stat.change}
            </p>
          </div>
        ))}
      </div>

      <div className="h-64 sm:h-80 bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl flex items-center justify-center border border-slate-100">
        <div className="text-center p-4">
          <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <i className="ri-line-chart-line text-xl sm:text-2xl text-blue-600"></i>
          </div>
          <p className="text-slate-600 font-medium text-sm sm:text-base">Advanced Revenue Chart</p>
          <p className="text-xs sm:text-sm text-slate-500 mt-2">Interactive analytics with real-time data visualization</p>
        </div>
      </div>
    </div>
  );
}
