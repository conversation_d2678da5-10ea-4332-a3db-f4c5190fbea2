'use client';

import React from 'react';

export default function RevenueDistribution() {
  const revenueData = [
    { source: 'Hotel Commissions', amount: '$1,847,290', percentage: 74, color: 'bg-blue-500' },
    { source: 'Service Fees', amount: '$386,470', percentage: 16, color: 'bg-emerald-500' },
    { source: 'Premium Features', amount: '$189,340', percentage: 8, color: 'bg-purple-500' },
    { source: 'Partnership Revenue', amount: '$89,900', percentage: 2, color: 'bg-amber-500' }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden">
      <h3 className="font-semibold text-slate-900 mb-6 text-lg">Revenue Distribution</h3>
      <div className="space-y-4">
        {revenueData.map((item, index) => (
          <div key={index}>
            <div className="flex justify-between items-center mb-2 gap-3">
              <div className="flex items-center space-x-2 min-w-0 flex-1">
                <div className={`w-3 h-3 rounded-full ${item.color} flex-shrink-0`}></div>
                <span className="text-sm text-slate-600 truncate">{item.source}</span>
              </div>
              <span className="text-sm font-semibold text-slate-900 whitespace-nowrap">{item.amount}</span>
            </div>
            <div className="w-full bg-slate-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${item.color} transition-all duration-500`}
                style={{ width: `${item.percentage}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
