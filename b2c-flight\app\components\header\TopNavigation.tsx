
'use client';

import React, { useState, useEffect } from 'react';

export default function TopNavigation() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <nav
      className="header px-responsive py-3 shadow-sm flex-shrink-0 h-16 w-full"
      style={{
        backgroundColor: 'var(--color-header-bg)',
        borderBottom: '1px solid var(--color-header-border)'
      }}
    >
      <div className="flex items-center justify-between">
        {/* Logo and Search Section */}
        <div className="flex items-center space-x-4 lg:space-x-6 flex-1 min-w-0">
          <div className="flex items-center space-x-2 flex-shrink-0">
            <div 
              className="w-8 h-8 rounded-lg flex items-center justify-center"
              style={{
                background: 'linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))'
              }}
            >
              <i className="ri-building-2-fill text-white text-sm"></i>
            </div>
            {!isMobile && (
              <div>
                <div className="font-pacifico text-responsive-lg" style={{ color: 'var(--color-text-primary)' }}>
                  TravelAdmin
                </div>
                <div className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>
                  Enterprise Dashboard
                </div>
              </div>
            )}
          </div>
          
          {/* Responsive Search */}
          <div className="relative flex-1 max-w-sm lg:max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i className="ri-search-line text-sm" style={{ color: 'var(--color-text-muted)' }}></i>
            </div>
            <input
              type="text"
              placeholder={isMobile ? "Search..." : "Search bookings, users, hotels..."}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input pl-9 pr-3 py-2 w-full text-responsive-sm placeholder-opacity-75 transition-all"
              style={{
                backgroundColor: 'var(--color-surface-alt)',
                border: '1px solid var(--color-border)',
                color: 'var(--color-text-primary)'
              } as React.CSSProperties}
            />
            {!isMobile && (
              <div className="absolute inset-y-0 right-0 pr-2 flex items-center">
                <kbd 
                  className="inline-flex items-center rounded px-1.5 py-0.5 text-xs font-medium"
                  style={{
                    color: 'var(--color-text-tertiary)',
                    backgroundColor: 'var(--color-surface)'
                  }}
                >
                  ⌘K
                </kbd>
              </div>
            )}
          </div>
        </div>

        {/* Right Section - Stats, Notifications, Profile */}
        <div className="flex items-center space-x-2 lg:space-x-4">
          {/* System Status - Hidden on mobile */}
          {!isMobile && (
            <div 
              className="flex items-center space-x-2 px-3 py-1.5 rounded-lg border"
              style={{
                backgroundColor: 'color-mix(in srgb, var(--color-success-500) 10%, transparent)',
                borderColor: 'color-mix(in srgb, var(--color-success-500) 20%, transparent)'
              }}
            >
              <div 
                className="w-2 h-2 rounded-full animate-pulse"
                style={{ backgroundColor: 'var(--color-success-500)' }}
              ></div>
              <span className="text-xs font-medium" style={{ color: 'var(--color-success-700)' }}>
                All Systems Operational
              </span>
              <div className="text-xs" style={{ color: 'var(--color-success-600)' }}>99.9%</div>
            </div>
          )}

          {/* Quick Stats - Responsive */}
          {!isMobile && (
            <div 
              className="flex items-center space-x-3 px-3 py-1.5 rounded-lg"
              style={{ backgroundColor: 'var(--color-surface-alt)' }}
            >
              <div className="text-center">
                <div className="text-responsive-sm font-bold" style={{ color: 'var(--color-text-primary)' }}>
                  $42.8K
                </div>
                <div className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>Today</div>
              </div>
              <div 
                className="w-px h-6"
                style={{ backgroundColor: 'var(--color-border)' }}
              ></div>
              <div className="text-center">
                <div className="text-responsive-sm font-bold" style={{ color: 'var(--color-text-primary)' }}>
                  847
                </div>
                <div className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>Active</div>
              </div>
            </div>
          )}

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 rounded-lg transition-colors touch-target"
              style={{
                color: 'var(--color-text-secondary)'
              } as React.CSSProperties}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <i className="ri-notification-line text-lg"></i>
              <span 
                className="absolute -top-1 -right-1 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-medium"
                style={{ backgroundColor: 'var(--color-error-500)' }}
              >
                3
              </span>
            </button>
            
            {showNotifications && (
              <div 
                className="absolute right-0 mt-2 w-80 rounded-xl shadow-xl border z-50 modal-responsive animate-scale-in"
                style={{
                  backgroundColor: 'var(--color-modal-bg)',
                  borderColor: 'var(--color-border)'
                }}
              >
                <div 
                  className="p-4 border-b"
                  style={{ borderColor: 'var(--color-border)' }}
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold" style={{ color: 'var(--color-text-primary)' }}>Notifications</h3>
                    <button className="text-sm font-medium" style={{ color: 'var(--color-primary-600)' }}>
                      Mark all read
                    </button>
                  </div>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {[
                    {
                      icon: 'ri-alert-line',
                      iconColor: 'var(--color-error-600)',
                      iconBg: 'color-mix(in srgb, var(--color-error-600) 15%, transparent)',
                      title: 'Payment Gateway Alert',
                      message: 'High volume of failed transactions detected',
                      time: '2 minutes ago',
                      unread: true
                    },
                    {
                      icon: 'ri-customer-service-line',
                      iconColor: 'var(--color-warning-600)',
                      iconBg: 'color-mix(in srgb, var(--color-warning-600) 15%, transparent)',
                      title: 'Support Queue Alert',
                      message: '15 tickets pending - priority response needed',
                      time: '8 minutes ago',
                      unread: true
                    },
                    {
                      icon: 'ri-building-line',
                      iconColor: 'var(--color-primary-600)',
                      iconBg: 'color-mix(in srgb, var(--color-primary-600) 15%, transparent)',
                      title: 'New Partner Integration',
                      message: 'Marriott Group has completed API integration',
                      time: '1 hour ago',
                      unread: false
                    }
                  ].map((notification, index) => (
                    <div 
                      key={index}
                      className="p-3 border-b transition-colors cursor-pointer"
                      style={{
                        borderColor: 'var(--color-border-light)'
                      } as React.CSSProperties}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <div className="flex items-start space-x-3">
                        <div 
                          className="w-8 h-8 rounded-lg flex items-center justify-center"
                          style={{ backgroundColor: notification.iconBg }}
                        >
                          <i className={`${notification.icon} text-sm`} style={{ color: notification.iconColor }}></i>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium" style={{ color: 'var(--color-text-primary)' }}>
                            {notification.title}
                          </p>
                          <p className="text-xs mt-1" style={{ color: 'var(--color-text-secondary)' }}>
                            {notification.message}
                          </p>
                          <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
                            {notification.time}
                          </p>
                        </div>
                        {notification.unread && (
                          <div 
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: 'var(--color-error-500)' }}
                          ></div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                <div 
                  className="p-3 border-t"
                  style={{ borderColor: 'var(--color-border)' }}
                >
                  <button 
                    className="w-full text-center text-sm font-medium"
                    style={{ color: 'var(--color-primary-600)' }}
                  >
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Profile */}
          <div className="relative">
            <button
              onClick={() => setShowProfile(!showProfile)}
              className="flex items-center space-x-2 p-1.5 rounded-lg transition-colors touch-target"
              style={{} as React.CSSProperties}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center"
                style={{
                  background: 'linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))'
                }}
              >
                <span className="text-white text-xs font-semibold">JD</span>
              </div>
              {!isMobile && (
                <>
                  <div className="text-left">
                    <p className="text-sm font-semibold" style={{ color: 'var(--color-text-primary)' }}>John Doe</p>
                    <p className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>Operations Manager</p>
                  </div>
                  <i className="ri-arrow-down-s-line" style={{ color: 'var(--color-text-muted)' }}></i>
                </>
              )}
            </button>
            
            {showProfile && (
              <div 
                className="absolute right-0 mt-2 w-56 rounded-xl shadow-xl border z-50 animate-scale-in"
                style={{
                  backgroundColor: 'var(--color-modal-bg)',
                  borderColor: 'var(--color-border)'
                }}
              >
                <div 
                  className="p-3 border-b"
                  style={{ borderColor: 'var(--color-border)' }}
                >
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-10 h-10 rounded-lg flex items-center justify-center"
                      style={{
                        background: 'linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))'
                      }}
                    >
                      <span className="text-white font-semibold text-sm">JD</span>
                    </div>
                    <div className="min-w-0">
                      <p className="font-semibold text-sm" style={{ color: 'var(--color-text-primary)' }}>John Doe</p>
                      <p className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>Operations Manager</p>
                      <p className="text-xs" style={{ color: 'var(--color-text-muted)' }}>Premium Access</p>
                    </div>
                  </div>
                </div>
                <div className="py-1">
                  {[
                    { icon: 'ri-user-line', label: 'Profile Settings' },
                    { icon: 'ri-settings-line', label: 'Account Preferences' },
                    { icon: 'ri-shield-user-line', label: 'Security Settings' }
                  ].map((item, index) => (
                    <a 
                      key={index}
                      href="#" 
                      className="flex items-center space-x-2 px-3 py-2 text-sm transition-colors"
                      style={{
                        color: 'var(--color-text-secondary)'
                      } as React.CSSProperties}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <i className={item.icon} style={{ color: 'var(--color-text-muted)' }}></i>
                      <span>{item.label}</span>
                    </a>
                  ))}
                  <div 
                    className="border-t my-1"
                    style={{ borderColor: 'var(--color-border)' }}
                  ></div>
                  <a 
                    href="#" 
                    className="flex items-center space-x-2 px-3 py-2 text-sm transition-colors"
                    style={{
                      color: 'var(--color-text-secondary)'
                    } as React.CSSProperties}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    <i className="ri-logout-box-line" style={{ color: 'var(--color-text-muted)' }}></i>
                    <span>Sign Out</span>
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
