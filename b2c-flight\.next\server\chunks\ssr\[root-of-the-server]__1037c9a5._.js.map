{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/header/TopNavigation.tsx"], "sourcesContent": ["\r\n'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\n\r\nexport default function TopNavigation() {\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [showNotifications, setShowNotifications] = useState(false);\r\n  const [showProfile, setShowProfile] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const target = event.target as Element;\r\n      if (!target.closest('.dropdown-container')) {\r\n        setShowNotifications(false);\r\n        setShowProfile(false);\r\n      }\r\n    };\r\n\r\n    handleResize();\r\n    window.addEventListener('resize', handleResize);\r\n    document.addEventListener('click', handleClickOutside);\r\n\r\n    return () => {\r\n      window.removeEventListener('resize', handleResize);\r\n      document.removeEventListener('click', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <nav\r\n      className=\"header px-responsive py-3 shadow-sm flex-shrink-0 h-16 w-full\"\r\n      style={{\r\n        backgroundColor: 'var(--color-header-bg)',\r\n        borderBottom: '1px solid var(--color-header-border)'\r\n      }}\r\n    >\r\n      <div className=\"flex items-center justify-between\">\r\n        {/* Logo and Search Section */}\r\n        <div className=\"flex items-center space-x-4 lg:space-x-6 flex-1 min-w-0\">\r\n          <div className=\"flex items-center space-x-2 flex-shrink-0\">\r\n            <div \r\n              className=\"w-8 h-8 rounded-lg flex items-center justify-center\"\r\n              style={{\r\n                background: 'linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))'\r\n              }}\r\n            >\r\n              <i className=\"ri-building-2-fill text-white text-sm\"></i>\r\n            </div>\r\n            {!isMobile && (\r\n              <div>\r\n                <div className=\"font-pacifico text-responsive-lg\" style={{ color: 'var(--color-text-primary)' }}>\r\n                  TravelAdmin\r\n                </div>\r\n                <div className=\"text-xs\" style={{ color: 'var(--color-text-tertiary)' }}>\r\n                  Enterprise Dashboard\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* Responsive Search */}\r\n          <div className=\"relative flex-1 max-w-sm lg:max-w-md\">\r\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <i className=\"ri-search-line text-sm\" style={{ color: 'var(--color-text-muted)' }}></i>\r\n            </div>\r\n            <input\r\n              type=\"text\"\r\n              placeholder={isMobile ? \"Search...\" : \"Search bookings, users, hotels...\"}\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"input pl-9 pr-3 py-2 w-full text-responsive-sm placeholder-opacity-75 transition-all\"\r\n              style={{\r\n                backgroundColor: 'var(--color-surface-alt)',\r\n                border: '1px solid var(--color-border)',\r\n                color: 'var(--color-text-primary)'\r\n              } as React.CSSProperties}\r\n            />\r\n            {!isMobile && (\r\n              <div className=\"absolute inset-y-0 right-0 pr-2 flex items-center\">\r\n                <kbd \r\n                  className=\"inline-flex items-center rounded px-1.5 py-0.5 text-xs font-medium\"\r\n                  style={{\r\n                    color: 'var(--color-text-tertiary)',\r\n                    backgroundColor: 'var(--color-surface)'\r\n                  }}\r\n                >\r\n                  ⌘K\r\n                </kbd>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Section - Stats, Notifications, Profile */}\r\n        <div className=\"flex items-center space-x-2 lg:space-x-4\">\r\n          {/* System Status - Hidden on mobile */}\r\n          {!isMobile && (\r\n            <div \r\n              className=\"flex items-center space-x-2 px-3 py-1.5 rounded-lg border\"\r\n              style={{\r\n                backgroundColor: 'rgba(34, 197, 94, 0.1)',\r\n                borderColor: 'rgba(34, 197, 94, 0.2)'\r\n              }}\r\n            >\r\n              <div \r\n                className=\"w-2 h-2 rounded-full animate-pulse\"\r\n                style={{ backgroundColor: 'var(--color-success-500)' }}\r\n              ></div>\r\n              <span className=\"text-xs font-medium\" style={{ color: 'var(--color-success-700)' }}>\r\n                All Systems Operational\r\n              </span>\r\n              <div className=\"text-xs\" style={{ color: 'var(--color-success-600)' }}>99.9%</div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Quick Stats - Responsive */}\r\n          {!isMobile && (\r\n            <div \r\n              className=\"flex items-center space-x-3 px-3 py-1.5 rounded-lg\"\r\n              style={{ backgroundColor: 'var(--color-surface-alt)' }}\r\n            >\r\n              <div className=\"text-center\">\r\n                <div className=\"text-responsive-sm font-bold\" style={{ color: 'var(--color-text-primary)' }}>\r\n                  $42.8K\r\n                </div>\r\n                <div className=\"text-xs\" style={{ color: 'var(--color-text-tertiary)' }}>Today</div>\r\n              </div>\r\n              <div \r\n                className=\"w-px h-6\"\r\n                style={{ backgroundColor: 'var(--color-border)' }}\r\n              ></div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-responsive-sm font-bold\" style={{ color: 'var(--color-text-primary)' }}>\r\n                  847\r\n                </div>\r\n                <div className=\"text-xs\" style={{ color: 'var(--color-text-tertiary)' }}>Active</div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Notifications */}\r\n          <div className=\"relative dropdown-container\">\r\n            <button\r\n              onClick={() => setShowNotifications(!showNotifications)}\r\n              className=\"relative p-2 rounded-lg transition-colors touch-target\"\r\n              style={{\r\n                color: 'var(--color-text-secondary)'\r\n              } as React.CSSProperties}\r\n              onMouseEnter={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'transparent';\r\n              }}\r\n            >\r\n              <i className=\"ri-notification-line text-lg\"></i>\r\n              <span \r\n                className=\"absolute -top-1 -right-1 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-medium\"\r\n                style={{ backgroundColor: 'var(--color-error-500)' }}\r\n              >\r\n                3\r\n              </span>\r\n            </button>\r\n            \r\n            {showNotifications && (\r\n              <div \r\n                className=\"absolute right-0 mt-2 w-80 rounded-xl shadow-xl border z-50 modal-responsive animate-scale-in\"\r\n                style={{\r\n                  backgroundColor: 'var(--color-modal-bg)',\r\n                  borderColor: 'var(--color-border)'\r\n                }}\r\n              >\r\n                <div \r\n                  className=\"p-4 border-b\"\r\n                  style={{ borderColor: 'var(--color-border)' }}\r\n                >\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <h3 className=\"font-semibold\" style={{ color: 'var(--color-text-primary)' }}>Notifications</h3>\r\n                    <button className=\"text-sm font-medium\" style={{ color: 'var(--color-primary-600)' }}>\r\n                      Mark all read\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div className=\"max-h-64 overflow-y-auto\">\r\n                  {[\r\n                    {\r\n                      icon: 'ri-alert-line',\r\n                      iconColor: 'var(--color-error-600)',\r\n                      iconBg: 'rgba(220, 38, 38, 0.15)',\r\n                      title: 'Payment Gateway Alert',\r\n                      message: 'High volume of failed transactions detected',\r\n                      time: '2 minutes ago',\r\n                      unread: true\r\n                    },\r\n                    {\r\n                      icon: 'ri-customer-service-line',\r\n                      iconColor: 'var(--color-warning-600)',\r\n                      iconBg: 'rgba(217, 119, 6, 0.15)',\r\n                      title: 'Support Queue Alert',\r\n                      message: '15 tickets pending - priority response needed',\r\n                      time: '8 minutes ago',\r\n                      unread: true\r\n                    },\r\n                    {\r\n                      icon: 'ri-building-line',\r\n                      iconColor: 'var(--color-primary-600)',\r\n                      iconBg: 'rgba(37, 99, 235, 0.15)',\r\n                      title: 'New Partner Integration',\r\n                      message: 'Marriott Group has completed API integration',\r\n                      time: '1 hour ago',\r\n                      unread: false\r\n                    }\r\n                  ].map((notification, index) => (\r\n                    <div \r\n                      key={index}\r\n                      className=\"p-3 border-b transition-colors cursor-pointer\"\r\n                      style={{\r\n                        borderColor: 'var(--color-border-light)'\r\n                      } as React.CSSProperties}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}\r\n                    >\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div \r\n                          className=\"w-8 h-8 rounded-lg flex items-center justify-center\"\r\n                          style={{ backgroundColor: notification.iconBg }}\r\n                        >\r\n                          <i className={`${notification.icon} text-sm`} style={{ color: notification.iconColor }}></i>\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <p className=\"text-sm font-medium\" style={{ color: 'var(--color-text-primary)' }}>\r\n                            {notification.title}\r\n                          </p>\r\n                          <p className=\"text-xs mt-1\" style={{ color: 'var(--color-text-secondary)' }}>\r\n                            {notification.message}\r\n                          </p>\r\n                          <p className=\"text-xs mt-1\" style={{ color: 'var(--color-text-muted)' }}>\r\n                            {notification.time}\r\n                          </p>\r\n                        </div>\r\n                        {notification.unread && (\r\n                          <div \r\n                            className=\"w-2 h-2 rounded-full\"\r\n                            style={{ backgroundColor: 'var(--color-error-500)' }}\r\n                          ></div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n                <div \r\n                  className=\"p-3 border-t\"\r\n                  style={{ borderColor: 'var(--color-border)' }}\r\n                >\r\n                  <button \r\n                    className=\"w-full text-center text-sm font-medium\"\r\n                    style={{ color: 'var(--color-primary-600)' }}\r\n                  >\r\n                    View all notifications\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Profile */}\r\n          <div className=\"relative dropdown-container\">\r\n            <button\r\n              onClick={() => setShowProfile(!showProfile)}\r\n              className=\"flex items-center space-x-2 p-1.5 rounded-lg transition-colors touch-target\"\r\n              style={{} as React.CSSProperties}\r\n              onMouseEnter={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.currentTarget.style.backgroundColor = 'transparent';\r\n              }}\r\n            >\r\n              <div \r\n                className=\"w-8 h-8 rounded-lg flex items-center justify-center\"\r\n                style={{\r\n                  background: 'linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))'\r\n                }}\r\n              >\r\n                <span className=\"text-white text-xs font-semibold\">JD</span>\r\n              </div>\r\n              {!isMobile && (\r\n                <>\r\n                  <div className=\"text-left\">\r\n                    <p className=\"text-sm font-semibold\" style={{ color: 'var(--color-text-primary)' }}>John Doe</p>\r\n                    <p className=\"text-xs\" style={{ color: 'var(--color-text-tertiary)' }}>Operations Manager</p>\r\n                  </div>\r\n                  <i className=\"ri-arrow-down-s-line\" style={{ color: 'var(--color-text-muted)' }}></i>\r\n                </>\r\n              )}\r\n            </button>\r\n            \r\n            {showProfile && (\r\n              <div \r\n                className=\"absolute right-0 mt-2 w-56 rounded-xl shadow-xl border z-50 animate-scale-in\"\r\n                style={{\r\n                  backgroundColor: 'var(--color-modal-bg)',\r\n                  borderColor: 'var(--color-border)'\r\n                }}\r\n              >\r\n                <div \r\n                  className=\"p-3 border-b\"\r\n                  style={{ borderColor: 'var(--color-border)' }}\r\n                >\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <div \r\n                      className=\"w-10 h-10 rounded-lg flex items-center justify-center\"\r\n                      style={{\r\n                        background: 'linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))'\r\n                      }}\r\n                    >\r\n                      <span className=\"text-white font-semibold text-sm\">JD</span>\r\n                    </div>\r\n                    <div className=\"min-w-0\">\r\n                      <p className=\"font-semibold text-sm\" style={{ color: 'var(--color-text-primary)' }}>John Doe</p>\r\n                      <p className=\"text-xs\" style={{ color: 'var(--color-text-tertiary)' }}>Operations Manager</p>\r\n                      <p className=\"text-xs\" style={{ color: 'var(--color-text-muted)' }}>Premium Access</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"py-1\">\r\n                  {[\r\n                    { icon: 'ri-user-line', label: 'Profile Settings' },\r\n                    { icon: 'ri-settings-line', label: 'Account Preferences' },\r\n                    { icon: 'ri-shield-user-line', label: 'Security Settings' }\r\n                  ].map((item, index) => (\r\n                    <a \r\n                      key={index}\r\n                      href=\"#\" \r\n                      className=\"flex items-center space-x-2 px-3 py-2 text-sm transition-colors\"\r\n                      style={{\r\n                        color: 'var(--color-text-secondary)'\r\n                      } as React.CSSProperties}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}\r\n                    >\r\n                      <i className={item.icon} style={{ color: 'var(--color-text-muted)' }}></i>\r\n                      <span>{item.label}</span>\r\n                    </a>\r\n                  ))}\r\n                  <div \r\n                    className=\"border-t my-1\"\r\n                    style={{ borderColor: 'var(--color-border)' }}\r\n                  ></div>\r\n                  <a \r\n                    href=\"#\" \r\n                    className=\"flex items-center space-x-2 px-3 py-2 text-sm transition-colors\"\r\n                    style={{\r\n                      color: 'var(--color-text-secondary)'\r\n                    } as React.CSSProperties}\r\n                    onMouseEnter={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'var(--color-surface-alt)';\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }}\r\n                  >\r\n                    <i className=\"ri-logout-box-line\" style={{ color: 'var(--color-text-muted)' }}></i>\r\n                    <span>Sign Out</span>\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kOAAQ,EAAC;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,kOAAQ,EAAC;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kOAAQ,EAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,kOAAQ,EAAC;IAEzC,IAAA,mOAAS,EAAC;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA,MAAM,qBAAqB,CAAC;YAC1B,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,wBAAwB;gBAC1C,qBAAqB;gBACrB,eAAe;YACjB;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,SAAS,gBAAgB,CAAC,SAAS;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,SAAS,mBAAmB,CAAC,SAAS;QACxC;IACF,GAAG,EAAE;IAEL,qBACE,+PAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,cAAc;QAChB;kBAEA,cAAA,+PAAC;YAAI,WAAU;;8BAEb,+PAAC;oBAAI,WAAU;;sCACb,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY;oCACd;8CAEA,cAAA,+PAAC;wCAAE,WAAU;;;;;;;;;;;gCAEd,CAAC,0BACA,+PAAC;;sDACC,+PAAC;4CAAI,WAAU;4CAAmC,OAAO;gDAAE,OAAO;4CAA4B;sDAAG;;;;;;sDAGjG,+PAAC;4CAAI,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAA6B;sDAAG;;;;;;;;;;;;;;;;;;sCAQ/E,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAI,WAAU;8CACb,cAAA,+PAAC;wCAAE,WAAU;wCAAyB,OAAO;4CAAE,OAAO;wCAA0B;;;;;;;;;;;8CAElF,+PAAC;oCACC,MAAK;oCACL,aAAa,WAAW,cAAc;oCACtC,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,QAAQ;wCACR,OAAO;oCACT;;;;;;gCAED,CAAC,0BACA,+PAAC;oCAAI,WAAU;8CACb,cAAA,+PAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;kDACD;;;;;;;;;;;;;;;;;;;;;;;8BAST,+PAAC;oBAAI,WAAU;;wBAEZ,CAAC,0BACA,+PAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB;gCACjB,aAAa;4BACf;;8CAEA,+PAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB;oCAA2B;;;;;;8CAEvD,+PAAC;oCAAK,WAAU;oCAAsB,OAAO;wCAAE,OAAO;oCAA2B;8CAAG;;;;;;8CAGpF,+PAAC;oCAAI,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAA2B;8CAAG;;;;;;;;;;;;wBAK1E,CAAC,0BACA,+PAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAA2B;;8CAErD,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAI,WAAU;4CAA+B,OAAO;gDAAE,OAAO;4CAA4B;sDAAG;;;;;;sDAG7F,+PAAC;4CAAI,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAA6B;sDAAG;;;;;;;;;;;;8CAE3E,+PAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB;oCAAsB;;;;;;8CAElD,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAI,WAAU;4CAA+B,OAAO;gDAAE,OAAO;4CAA4B;sDAAG;;;;;;sDAG7F,+PAAC;4CAAI,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAA6B;sDAAG;;;;;;;;;;;;;;;;;;sCAM/E,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCACC,SAAS,IAAM,qBAAqB,CAAC;oCACrC,WAAU;oCACV,OAAO;wCACL,OAAO;oCACT;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;;sDAEA,+PAAC;4CAAE,WAAU;;;;;;sDACb,+PAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAyB;sDACpD;;;;;;;;;;;;gCAKF,mCACC,+PAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,aAAa;oCACf;;sDAEA,+PAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAsB;sDAE5C,cAAA,+PAAC;gDAAI,WAAU;;kEACb,+PAAC;wDAAG,WAAU;wDAAgB,OAAO;4DAAE,OAAO;wDAA4B;kEAAG;;;;;;kEAC7E,+PAAC;wDAAO,WAAU;wDAAsB,OAAO;4DAAE,OAAO;wDAA2B;kEAAG;;;;;;;;;;;;;;;;;sDAK1F,+PAAC;4CAAI,WAAU;sDACZ;gDACC;oDACE,MAAM;oDACN,WAAW;oDACX,QAAQ;oDACR,OAAO;oDACP,SAAS;oDACT,MAAM;oDACN,QAAQ;gDACV;gDACA;oDACE,MAAM;oDACN,WAAW;oDACX,QAAQ;oDACR,OAAO;oDACP,SAAS;oDACT,MAAM;oDACN,QAAQ;gDACV;gDACA;oDACE,MAAM;oDACN,WAAW;oDACX,QAAQ;oDACR,OAAO;oDACP,SAAS;oDACT,MAAM;oDACN,QAAQ;gDACV;6CACD,CAAC,GAAG,CAAC,CAAC,cAAc,sBACnB,+PAAC;oDAEC,WAAU;oDACV,OAAO;wDACL,aAAa;oDACf;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;8DAEA,cAAA,+PAAC;wDAAI,WAAU;;0EACb,+PAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,aAAa,MAAM;gEAAC;0EAE9C,cAAA,+PAAC;oEAAE,WAAW,GAAG,aAAa,IAAI,CAAC,QAAQ,CAAC;oEAAE,OAAO;wEAAE,OAAO,aAAa,SAAS;oEAAC;;;;;;;;;;;0EAEvF,+PAAC;gEAAI,WAAU;;kFACb,+PAAC;wEAAE,WAAU;wEAAsB,OAAO;4EAAE,OAAO;wEAA4B;kFAC5E,aAAa,KAAK;;;;;;kFAErB,+PAAC;wEAAE,WAAU;wEAAe,OAAO;4EAAE,OAAO;wEAA8B;kFACvE,aAAa,OAAO;;;;;;kFAEvB,+PAAC;wEAAE,WAAU;wEAAe,OAAO;4EAAE,OAAO;wEAA0B;kFACnE,aAAa,IAAI;;;;;;;;;;;;4DAGrB,aAAa,MAAM,kBAClB,+PAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB;gEAAyB;;;;;;;;;;;;mDAjCpD;;;;;;;;;;sDAwCX,+PAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAsB;sDAE5C,cAAA,+PAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA2B;0DAC5C;;;;;;;;;;;;;;;;;;;;;;;sCAST,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;oCACV,OAAO,CAAC;oCACR,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;;sDAEA,+PAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY;4CACd;sDAEA,cAAA,+PAAC;gDAAK,WAAU;0DAAmC;;;;;;;;;;;wCAEpD,CAAC,0BACA;;8DACE,+PAAC;oDAAI,WAAU;;sEACb,+PAAC;4DAAE,WAAU;4DAAwB,OAAO;gEAAE,OAAO;4DAA4B;sEAAG;;;;;;sEACpF,+PAAC;4DAAE,WAAU;4DAAU,OAAO;gEAAE,OAAO;4DAA6B;sEAAG;;;;;;;;;;;;8DAEzE,+PAAC;oDAAE,WAAU;oDAAuB,OAAO;wDAAE,OAAO;oDAA0B;;;;;;;;;;;;;;gCAKnF,6BACC,+PAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,aAAa;oCACf;;sDAEA,+PAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAsB;sDAE5C,cAAA,+PAAC;gDAAI,WAAU;;kEACb,+PAAC;wDACC,WAAU;wDACV,OAAO;4DACL,YAAY;wDACd;kEAEA,cAAA,+PAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;kEAErD,+PAAC;wDAAI,WAAU;;0EACb,+PAAC;gEAAE,WAAU;gEAAwB,OAAO;oEAAE,OAAO;gEAA4B;0EAAG;;;;;;0EACpF,+PAAC;gEAAE,WAAU;gEAAU,OAAO;oEAAE,OAAO;gEAA6B;0EAAG;;;;;;0EACvE,+PAAC;gEAAE,WAAU;gEAAU,OAAO;oEAAE,OAAO;gEAA0B;0EAAG;;;;;;;;;;;;;;;;;;;;;;;sDAI1E,+PAAC;4CAAI,WAAU;;gDACZ;oDACC;wDAAE,MAAM;wDAAgB,OAAO;oDAAmB;oDAClD;wDAAE,MAAM;wDAAoB,OAAO;oDAAsB;oDACzD;wDAAE,MAAM;wDAAuB,OAAO;oDAAoB;iDAC3D,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,+PAAC;wDAEC,MAAK;wDACL,WAAU;wDACV,OAAO;4DACL,OAAO;wDACT;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wDAC1C;wDACA,cAAc,CAAC;4DACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wDAC1C;;0EAEA,+PAAC;gEAAE,WAAW,KAAK,IAAI;gEAAE,OAAO;oEAAE,OAAO;gEAA0B;;;;;;0EACnE,+PAAC;0EAAM,KAAK,KAAK;;;;;;;uDAdZ;;;;;8DAiBT,+PAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,aAAa;oDAAsB;;;;;;8DAE9C,+PAAC;oDACC,MAAK;oDACL,WAAU;oDACV,OAAO;wDACL,OAAO;oDACT;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;;sEAEA,+PAAC;4DAAE,WAAU;4DAAqB,OAAO;gEAAE,OAAO;4DAA0B;;;;;;sEAC5E,+PAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/sidebar/Sidebar.tsx"], "sourcesContent": ["\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\n\r\ninterface SidebarProps {\r\n  isCollapsed?: boolean;\r\n  onToggleCollapse?: () => void;\r\n  isMobile?: boolean;\r\n}\r\n\r\nexport default function Sidebar({\r\n  isCollapsed = false,\r\n  onToggleCollapse,\r\n  isMobile = false\r\n}: SidebarProps) {\r\n  const [activeItem, setActiveItem] = useState('Dashboard');\r\n\r\n  const menuItems = [\r\n    {\r\n      name: 'Dashboard',\r\n      icon: 'ri-dashboard-line',\r\n      href: '/',\r\n      active: true,\r\n    },\r\n    {\r\n      name: 'Airport',\r\n      icon: 'ri-hotel-line',\r\n      href: '/airport-master',\r\n      active: false,\r\n    },\r\n    {\r\n      name: 'Airlines',\r\n      icon: 'ri-calendar-check-line',\r\n      href: '/airlines',\r\n      active: false,\r\n    },\r\n    {\r\n      name: 'Customers',\r\n      icon: 'ri-building-2-line',\r\n      href: '/customers',\r\n      active: false,\r\n    },\r\n    {\r\n      name: 'Bookings',\r\n      icon: 'ri-price-tag-3-line',\r\n      href: '/bookings',\r\n      active: false,\r\n    }\r\n\r\n  ];\r\n\r\n  return (\r\n    <div\r\n      className={`sidebar h-full transition-all duration-300 ${\r\n        isCollapsed ? 'w-16' : 'w-64'\r\n      } flex flex-col shadow-xl flex-shrink-0 overflow-hidden`}\r\n      style={{\r\n        backgroundColor: 'var(--color-sidebar-bg)',\r\n        color: 'var(--color-sidebar-text)',\r\n        borderRight: '1px solid var(--color-sidebar-border)'\r\n      }}\r\n    >\r\n      {/* Responsive Header */}\r\n      <div \r\n        className=\"p-4 flex-shrink-0\"\r\n        style={{ borderBottom: '1px solid var(--color-sidebar-border)' }}\r\n      >\r\n        <button\r\n          onClick={onToggleCollapse}\r\n          className=\"w-full flex items-center justify-between rounded-lg p-2 transition-colors touch-target hover:bg-opacity-10\"\r\n          style={{\r\n            color: 'var(--color-sidebar-text)'\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            e.currentTarget.style.backgroundColor = 'rgba(203, 213, 225, 0.1)';\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            e.currentTarget.style.backgroundColor = 'transparent';\r\n          }}\r\n        >\r\n          {!isCollapsed && (\r\n            <div>\r\n              <span className=\"font-semibold text-responsive-sm\" style={{ color: 'var(--color-sidebar-text)' }}>\r\n                Navigation Menu\r\n              </span>\r\n              <p className=\"text-xs mt-0.5 opacity-75\">Enterprise Dashboard</p>\r\n            </div>\r\n          )}\r\n          <i\r\n            className={`ri-menu-fold-line opacity-75 ${\r\n              isCollapsed ? 'rotate-180' : ''\r\n            } transition-transform text-lg`}\r\n          ></i>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Responsive Menu Sections */}\r\n      <div className=\"flex-1 sidebar-scroll-container\">\r\n        <div className=\"overflow-y-auto py-2 sidebar-scrollbar h-full\">\r\n          <div className=\"px-2 space-y-1\">\r\n          {menuItems.map((item) => (\r\n            <Link key={item.name} href={item.href}>\r\n              <div\r\n                className={`sidebar-item flex items-center px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 touch-target ${\r\n                  activeItem === item.name ? 'active' : ''\r\n                }`}\r\n                onClick={() => setActiveItem(item.name)}\r\n                style={{\r\n                  backgroundColor: activeItem === item.name ? 'var(--color-primary-600)' : 'transparent',\r\n                  color: activeItem === item.name ? 'var(--color-sidebar-text-active)' : 'var(--color-sidebar-text)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (activeItem !== item.name) {\r\n                    e.currentTarget.style.backgroundColor = 'rgba(203, 213, 225, 0.1)';\r\n                    e.currentTarget.style.color = 'var(--color-sidebar-text-active)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (activeItem !== item.name) {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                    e.currentTarget.style.color = 'var(--color-sidebar-text)';\r\n                  }\r\n                }}\r\n              >\r\n                <div className=\"w-5 h-5 flex items-center justify-center flex-shrink-0\">\r\n                  <i className={`${item.icon} text-base`}></i>\r\n                </div>\r\n                {!isCollapsed && (\r\n                  <span className=\"text-responsive-sm font-medium ml-3 truncate\">\r\n                    {item.name}\r\n                  </span>\r\n                )}\r\n                {!isCollapsed && isMobile && (\r\n                  <i className=\"ri-arrow-right-s-line ml-auto opacity-50\"></i>\r\n                )}\r\n              </div>\r\n            </Link>\r\n          ))}\r\n        </div>\r\n\r\n          {/* Responsive Future Verticals */}\r\n          {!isCollapsed && (\r\n            <div className=\"px-4 mt-6\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <div className=\"text-xs font-semibold uppercase tracking-wider opacity-75\">\r\n                  Future Modules\r\n                </div>\r\n                <div\r\n                  className=\"px-2 py-0.5 rounded-full\"\r\n                  style={{ backgroundColor: 'rgba(203, 213, 225, 0.2)' }}\r\n                >\r\n                  <span className=\"text-xs opacity-75\">Soon</span>\r\n                </div>\r\n              </div>\r\n              <div className=\"space-y-1\">\r\n                {futureModules.map((item) => (\r\n                  <div\r\n                    key={item.name}\r\n                    className=\"flex items-center px-3 py-2.5 rounded-lg cursor-not-allowed opacity-50 touch-target\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center flex-shrink-0\">\r\n                      <i className={`${item.icon} text-base`}></i>\r\n                    </div>\r\n                    <span className=\"text-responsive-sm font-medium ml-3 truncate\">{item.name}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Responsive System Status Footer */}\r\n      <div \r\n        className=\"p-4 flex-shrink-0\"\r\n        style={{ borderTop: '1px solid var(--color-sidebar-border)' }}\r\n      >\r\n        <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>\r\n          <div \r\n            className=\"w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0\"\r\n            style={{ backgroundColor: 'var(--color-success-600)' }}\r\n          >\r\n            <i className=\"ri-shield-check-line text-white text-sm\"></i>\r\n          </div>\r\n          {!isCollapsed && (\r\n            <div className=\"min-w-0\">\r\n              <p className=\"text-responsive-sm font-medium truncate\" style={{ color: 'var(--color-sidebar-text)' }}>\r\n                System Status\r\n              </p>\r\n              <div className=\"flex items-center space-x-2 mt-0.5\">\r\n                <div \r\n                  className=\"w-2 h-2 rounded-full animate-pulse flex-shrink-0\"\r\n                  style={{ backgroundColor: 'var(--color-success-400)' }}\r\n                ></div>\r\n                <p className=\"text-xs truncate\" style={{ color: 'var(--color-success-400)' }}>\r\n                  All Systems Online\r\n                </p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAHA;;;;AAWe,SAAS,QAAQ,EAC9B,cAAc,KAAK,EACnB,gBAAgB,EAChB,WAAW,KAAK,EACH;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,kOAAQ,EAAC;IAE7C,MAAM,YAAY;QAChB;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACV;KAED;IAED,qBACE,+PAAC;QACC,WAAW,CAAC,2CAA2C,EACrD,cAAc,SAAS,OACxB,sDAAsD,CAAC;QACxD,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,aAAa;QACf;;0BAGA,+PAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,cAAc;gBAAwC;0BAE/D,cAAA,+PAAC;oBACC,SAAS;oBACT,WAAU;oBACV,OAAO;wBACL,OAAO;oBACT;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oBAC1C;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oBAC1C;;wBAEC,CAAC,6BACA,+PAAC;;8CACC,+PAAC;oCAAK,WAAU;oCAAmC,OAAO;wCAAE,OAAO;oCAA4B;8CAAG;;;;;;8CAGlG,+PAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;sCAG7C,+PAAC;4BACC,WAAW,CAAC,6BAA6B,EACvC,cAAc,eAAe,GAC9B,6BAA6B,CAAC;;;;;;;;;;;;;;;;;0BAMrC,+PAAC;gBAAI,WAAU;0BACb,cAAA,+PAAC;oBAAI,WAAU;;sCACb,+PAAC;4BAAI,WAAU;sCACd,UAAU,GAAG,CAAC,CAAC,qBACd,+PAAC,wLAAI;oCAAiB,MAAM,KAAK,IAAI;8CACnC,cAAA,+PAAC;wCACC,WAAW,CAAC,8GAA8G,EACxH,eAAe,KAAK,IAAI,GAAG,WAAW,IACtC;wCACF,SAAS,IAAM,cAAc,KAAK,IAAI;wCACtC,OAAO;4CACL,iBAAiB,eAAe,KAAK,IAAI,GAAG,6BAA6B;4CACzE,OAAO,eAAe,KAAK,IAAI,GAAG,qCAAqC;wCACzE;wCACA,cAAc,CAAC;4CACb,IAAI,eAAe,KAAK,IAAI,EAAE;gDAC5B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4CAChC;wCACF;wCACA,cAAc,CAAC;4CACb,IAAI,eAAe,KAAK,IAAI,EAAE;gDAC5B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4CAChC;wCACF;;0DAEA,+PAAC;gDAAI,WAAU;0DACb,cAAA,+PAAC;oDAAE,WAAW,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;;;;;;;;;;;4CAEvC,CAAC,6BACA,+PAAC;gDAAK,WAAU;0DACb,KAAK,IAAI;;;;;;4CAGb,CAAC,eAAe,0BACf,+PAAC;gDAAE,WAAU;;;;;;;;;;;;mCAhCR,KAAK,IAAI;;;;;;;;;;wBAwCrB,CAAC,6BACA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAI,WAAU;sDAA4D;;;;;;sDAG3E,+PAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAA2B;sDAErD,cAAA,+PAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;;8CAGzC,+PAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,+PAAC;4CAEC,WAAU;;8DAEV,+PAAC;oDAAI,WAAU;8DACb,cAAA,+PAAC;wDAAE,WAAW,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;;;;;;;;;;;8DAExC,+PAAC;oDAAK,WAAU;8DAAgD,KAAK,IAAI;;;;;;;2CANpE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgB5B,+PAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,WAAW;gBAAwC;0BAE5D,cAAA,+PAAC;oBAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,aAAa;;sCACjF,+PAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAA2B;sCAErD,cAAA,+PAAC;gCAAE,WAAU;;;;;;;;;;;wBAEd,CAAC,6BACA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAE,WAAU;oCAA0C,OAAO;wCAAE,OAAO;oCAA4B;8CAAG;;;;;;8CAGtG,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAA2B;;;;;;sDAEvD,+PAAC;4CAAE,WAAU;4CAAmB,OAAO;gDAAE,OAAO;4CAA2B;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9F", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/layout/LayoutWrapper.tsx"], "sourcesContent": ["'use client';\r\nimport React, { useState, useEffect } from 'react';\r\nimport TopNavigation from '../header/TopNavigation';\r\nimport Sidebar from '../sidebar/Sidebar';\r\n\r\ninterface LayoutWrapperProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport default function LayoutWrapper({ children }: LayoutWrapperProps) {\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const mobile = window.innerWidth < 768;\r\n      setIsMobile(mobile);\r\n      if (mobile) {\r\n        setSidebarCollapsed(true);\r\n      }\r\n    };\r\n\r\n    handleResize();\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  // Get sidebar width based on state\r\n  const getSidebarWidth = () => {\r\n    if (isMobile) {\r\n      return sidebarCollapsed ? 'w-0' : 'w-64';\r\n    }\r\n    return sidebarCollapsed ? 'w-16' : 'w-64';\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-screen w-full bg-slate-50 flex flex-col overflow-hidden\">\r\n      {/* Header - Fixed height of 64px (h-16) */}\r\n      <header className=\"w-full h-16 flex-shrink-0 z-40 bg-white border-b\">\r\n        <TopNavigation />\r\n      </header>\r\n\r\n      {/* Main Container - Exact remaining height (100vh - 64px) */}\r\n      <div className=\"flex w-full h-[calc(100vh-4rem)]\">\r\n        {/* Sidebar - Fixed Width, Full Height */}\r\n        <aside \r\n          className={`${getSidebarWidth()} flex-shrink-0 transition-all duration-300 ease-in-out relative z-30 h-full ${\r\n            isMobile && sidebarCollapsed ? 'overflow-hidden' : ''\r\n          }`}\r\n        >\r\n          <div className=\"h-full\">\r\n            <Sidebar\r\n              isCollapsed={sidebarCollapsed}\r\n              onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}\r\n              isMobile={isMobile}\r\n            />\r\n          </div>\r\n        </aside>\r\n\r\n        {/* Content Area - Remaining Width, Full Height */}\r\n        <main className=\"flex-1 overflow-y-auto overflow-x-hidden custom-scrollbar bg-slate-50 h-full\">\r\n          <div className=\"p-4 sm:p-6\">\r\n            {children}\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      {/* Mobile Overlay */}\r\n      {isMobile && !sidebarCollapsed && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-20\"\r\n          onClick={() => setSidebarCollapsed(true)}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAHA;;;;;AASe,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,kOAAQ,EAAC;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,kOAAQ,EAAC;IAEzC,IAAA,mOAAS,EAAC;QACR,MAAM,eAAe;YACnB,MAAM,SAAS,OAAO,UAAU,GAAG;YACnC,YAAY;YACZ,IAAI,QAAQ;gBACV,oBAAoB;YACtB;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,kBAAkB;QACtB,IAAI,UAAU;YACZ,OAAO,mBAAmB,QAAQ;QACpC;QACA,OAAO,mBAAmB,SAAS;IACrC;IAEA,qBACE,+PAAC;QAAI,WAAU;;0BAEb,+PAAC;gBAAO,WAAU;0BAChB,cAAA,+PAAC,yKAAa;;;;;;;;;;0BAIhB,+PAAC;gBAAI,WAAU;;kCAEb,+PAAC;wBACC,WAAW,GAAG,kBAAkB,4EAA4E,EAC1G,YAAY,mBAAmB,oBAAoB,IACnD;kCAEF,cAAA,+PAAC;4BAAI,WAAU;sCACb,cAAA,+PAAC,oKAAO;gCACN,aAAa;gCACb,kBAAkB,IAAM,oBAAoB,CAAC;gCAC7C,UAAU;;;;;;;;;;;;;;;;kCAMhB,+PAAC;wBAAK,WAAU;kCACd,cAAA,+PAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;YAMN,YAAY,CAAC,kCACZ,+PAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAK7C", "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/DashboardHeader.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\nexport default function DashboardHeader() {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl sm:text-3xl font-bold text-slate-900 mb-1\">Executive Dashboard</h1>\r\n          <p className=\"text-slate-600 text-sm sm:text-base\">Comprehensive overview of your travel platform operations</p>\r\n        </div>\r\n        <div className=\"flex items-center gap-3\">\r\n          <button className=\"px-3 py-2 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors text-sm font-medium whitespace-nowrap\">\r\n            <i className=\"ri-download-line mr-2\"></i>\r\n            Export Data\r\n          </button>\r\n          <button className=\"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium whitespace-nowrap\">\r\n            <i className=\"ri-add-line mr-2\"></i>\r\n            Quick Action\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n        <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-4 sm:p-6 text-white\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"min-w-0 flex-1\">\r\n              <p className=\"text-blue-100 text-xs sm:text-sm font-medium\">Live Performance</p>\r\n              <p className=\"text-xl sm:text-2xl font-bold truncate\">98.7%</p>\r\n              <p className=\"text-blue-100 text-xs sm:text-sm\">System Uptime</p>\r\n            </div>\r\n            <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-blue-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3\">\r\n              <i className=\"ri-pulse-line text-xl sm:text-2xl\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-xl p-4 sm:p-6 text-white\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"min-w-0 flex-1\">\r\n              <p className=\"text-emerald-100 text-xs sm:text-sm font-medium\">Active Sessions</p>\r\n              <p className=\"text-xl sm:text-2xl font-bold truncate\">2,847</p>\r\n              <p className=\"text-emerald-100 text-xs sm:text-sm\">Current Users</p>\r\n            </div>\r\n            <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-emerald-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3\">\r\n              <i className=\"ri-user-line text-xl sm:text-2xl\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl p-4 sm:p-6 text-white sm:col-span-2 lg:col-span-1\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"min-w-0 flex-1\">\r\n              <p className=\"text-purple-100 text-xs sm:text-sm font-medium\">Today&apos;s Revenue</p>\r\n              <p className=\"text-xl sm:text-2xl font-bold truncate\">$89.2K</p>\r\n              <p className=\"text-purple-100 text-xs sm:text-sm\">+12.3% vs yesterday</p>\r\n            </div>\r\n            <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-purple-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3\">\r\n              <i className=\"ri-trending-up-line text-xl sm:text-2xl\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAIe,SAAS;IACtB,qBACE,+PAAC;QAAI,WAAU;;0BACb,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;;0CACC,+PAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,+PAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;kCAErD,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAO,WAAU;;kDAChB,+PAAC;wCAAE,WAAU;;;;;;oCAA4B;;;;;;;0CAG3C,+PAAC;gCAAO,WAAU;;kDAChB,+PAAC;wCAAE,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;;0BAM1C,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;wBAAI,WAAU;kCACb,cAAA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAC5D,+PAAC;4CAAE,WAAU;sDAAyC;;;;;;sDACtD,+PAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAElD,+PAAC;oCAAI,WAAU;8CACb,cAAA,+PAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKnB,+PAAC;wBAAI,WAAU;kCACb,cAAA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAE,WAAU;sDAAkD;;;;;;sDAC/D,+PAAC;4CAAE,WAAU;sDAAyC;;;;;;sDACtD,+PAAC;4CAAE,WAAU;sDAAsC;;;;;;;;;;;;8CAErD,+PAAC;oCAAI,WAAU;8CACb,cAAA,+PAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKnB,+PAAC;wBAAI,WAAU;kCACb,cAAA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAE,WAAU;sDAAiD;;;;;;sDAC9D,+PAAC;4CAAE,WAAU;sDAAyC;;;;;;sDACtD,+PAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,+PAAC;oCAAI,WAAU;8CACb,cAAA,+PAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/DashboardStats.tsx"], "sourcesContent": ["\r\n'use client';\r\n\r\nimport React from 'react';\r\n\r\ntype ColorKey = 'blue' | 'emerald' | 'purple' | 'amber' | 'cyan' | 'rose';\r\ntype ChangeType = 'positive' | 'negative';\r\n\r\ninterface StatItem {\r\n  title: string;\r\n  value: string;\r\n  change: string;\r\n  changeType: ChangeType;\r\n  icon: string;\r\n  color: ColorKey;\r\n  trend: string;\r\n  subtitle: string;\r\n  additionalInfo: string;\r\n}\r\n\r\nexport default function DashboardStats() {\r\n  const stats: StatItem[] = [\r\n    {\r\n      title: 'Total Bookings',\r\n      value: '47,892',\r\n      change: '+18.2%',\r\n      changeType: 'positive',\r\n      icon: 'ri-calendar-check-fill',\r\n      color: 'blue',\r\n      trend: 'up',\r\n      subtitle: 'This month vs last month',\r\n      additionalInfo: '2,847 today'\r\n    },\r\n    {\r\n      title: 'Gross Revenue',\r\n      value: '$8.7M',\r\n      change: '+24.5%',\r\n      changeType: 'positive',\r\n      icon: 'ri-money-dollar-circle-fill',\r\n      color: 'emerald',\r\n      trend: 'up',\r\n      subtitle: 'Monthly recurring revenue',\r\n      additionalInfo: '$289K today'\r\n    },\r\n    {\r\n      title: 'Active Properties',\r\n      value: '2,847',\r\n      change: '+12.3%',\r\n      changeType: 'positive',\r\n      icon: 'ri-building-2-fill',\r\n      color: 'purple',\r\n      trend: 'up',\r\n      subtitle: 'Verified hotel partners',\r\n      additionalInfo: '47 new this week'\r\n    },\r\n    {\r\n      title: 'Customer Satisfaction',\r\n      value: '4.89',\r\n      change: '+0.12',\r\n      changeType: 'positive',\r\n      icon: 'ri-star-fill',\r\n      color: 'amber',\r\n      trend: 'up',\r\n      subtitle: 'Average rating score',\r\n      additionalInfo: '12,847 reviews'\r\n    },\r\n    {\r\n      title: 'Support Resolution',\r\n      value: '97.2%',\r\n      change: '****%',\r\n      changeType: 'positive',\r\n      icon: 'ri-customer-service-2-fill',\r\n      color: 'cyan',\r\n      trend: 'up',\r\n      subtitle: 'First contact resolution',\r\n      additionalInfo: 'Avg 4.2 min response'\r\n    },\r\n    {\r\n      title: 'Conversion Rate',\r\n      value: '12.8%',\r\n      change: '****%',\r\n      changeType: 'positive',\r\n      icon: 'ri-arrow-up-circle-fill',\r\n      color: 'rose',\r\n      trend: 'up',\r\n      subtitle: 'Visitor to booking ratio',\r\n      additionalInfo: 'Above industry avg'\r\n    }\r\n  ];\r\n\r\n  const colorClasses: Record<ColorKey, string> = {\r\n    blue: 'from-blue-500 to-blue-600',\r\n    emerald: 'from-emerald-500 to-emerald-600',\r\n    purple: 'from-purple-500 to-purple-600',\r\n    amber: 'from-amber-500 to-amber-600',\r\n    cyan: 'from-cyan-500 to-cyan-600',\r\n    rose: 'from-rose-500 to-rose-600'\r\n  };\r\n\r\n  const backgroundColors: Record<ColorKey, string> = {\r\n    blue: 'bg-blue-50 border-blue-100',\r\n    emerald: 'bg-emerald-50 border-emerald-100',\r\n    purple: 'bg-purple-50 border-purple-100',\r\n    amber: 'bg-amber-50 border-amber-100',\r\n    cyan: 'bg-cyan-50 border-cyan-100',\r\n    rose: 'bg-rose-50 border-rose-100'\r\n  };\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6\">\r\n      {stats.map((stat, index) => (\r\n        <div key={index} className={`bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow ${backgroundColors[stat.color]} overflow-hidden`}>\r\n          <div className=\"flex items-start justify-between mb-3 sm:mb-4\">\r\n            <div className={`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br ${colorClasses[stat.color]} rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0`}>\r\n              <i className={`${stat.icon} text-white text-lg sm:text-xl`}></i>\r\n            </div>\r\n            <div className=\"text-right ml-2\">\r\n              <div className={`inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ${\r\n                stat.changeType === 'positive' ? 'bg-emerald-100 text-emerald-700' : 'bg-red-100 text-red-700'\r\n              } whitespace-nowrap`}>\r\n                <i className={`${stat.changeType === 'positive' ? 'ri-arrow-up-line' : 'ri-arrow-down-line'} mr-1`}></i>\r\n                {stat.change}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mb-3 min-w-0\">\r\n            <h3 className=\"text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate\">{stat.value}</h3>\r\n            <p className=\"text-sm font-medium text-slate-600 truncate\">{stat.title}</p>\r\n          </div>\r\n\r\n          <div className=\"space-y-1 min-w-0\">\r\n            <p className=\"text-xs text-slate-500 truncate\">{stat.subtitle}</p>\r\n            <p className=\"text-xs text-slate-400 truncate\">{stat.additionalInfo}</p>\r\n          </div>\r\n\r\n          <div className=\"mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-slate-200\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-xs text-slate-500\">Trend</span>\r\n              <div className=\"flex items-center space-x-1\">\r\n                <div className=\"w-2 h-2 bg-emerald-500 rounded-full\"></div>\r\n                <div className=\"w-2 h-2 bg-emerald-400 rounded-full\"></div>\r\n                <div className=\"w-2 h-2 bg-emerald-300 rounded-full\"></div>\r\n                <div className=\"w-2 h-2 bg-slate-200 rounded-full\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AACA;;AAmBe,SAAS;IACtB,MAAM,QAAoB;QACxB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;YACV,gBAAgB;QAClB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;YACV,gBAAgB;QAClB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;YACV,gBAAgB;QAClB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;YACV,gBAAgB;QAClB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;YACV,gBAAgB;QAClB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,OAAO;YACP,OAAO;YACP,UAAU;YACV,gBAAgB;QAClB;KACD;IAED,MAAM,eAAyC;QAC7C,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,MAAM,mBAA6C;QACjD,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,qBACE,+PAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,+PAAC;gBAAgB,WAAW,CAAC,oGAAoG,EAAE,gBAAgB,CAAC,KAAK,KAAK,CAAC,CAAC,gBAAgB,CAAC;;kCAC/K,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAI,WAAW,CAAC,4CAA4C,EAAE,YAAY,CAAC,KAAK,KAAK,CAAC,CAAC,qEAAqE,CAAC;0CAC5J,cAAA,+PAAC;oCAAE,WAAW,GAAG,KAAK,IAAI,CAAC,8BAA8B,CAAC;;;;;;;;;;;0CAE5D,+PAAC;gCAAI,WAAU;0CACb,cAAA,+PAAC;oCAAI,WAAW,CAAC,8EAA8E,EAC7F,KAAK,UAAU,KAAK,aAAa,oCAAoC,0BACtE,kBAAkB,CAAC;;sDAClB,+PAAC;4CAAE,WAAW,GAAG,KAAK,UAAU,KAAK,aAAa,qBAAqB,qBAAqB,KAAK,CAAC;;;;;;wCACjG,KAAK,MAAM;;;;;;;;;;;;;;;;;;kCAKlB,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAG,WAAU;0CAA+D,KAAK,KAAK;;;;;;0CACvF,+PAAC;gCAAE,WAAU;0CAA+C,KAAK,KAAK;;;;;;;;;;;;kCAGxE,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAE,WAAU;0CAAmC,KAAK,QAAQ;;;;;;0CAC7D,+PAAC;gCAAE,WAAU;0CAAmC,KAAK,cAAc;;;;;;;;;;;;kCAGrE,+PAAC;wBAAI,WAAU;kCACb,cAAA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAK,WAAU;8CAAyB;;;;;;8CACzC,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAI,WAAU;;;;;;sDACf,+PAAC;4CAAI,WAAU;;;;;;sDACf,+PAAC;4CAAI,WAAU;;;;;;sDACf,+PAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;eAhCb;;;;;;;;;;AAwClB", "debugId": null}}, {"offset": {"line": 1951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/RevenueAnalytics.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\n\r\nexport default function RevenueAnalytics() {\r\n  const [activeTimeframe, setActiveTimeframe] = useState('7D');\r\n\r\n  const timeframes = ['7D', '30D', '90D', '1Y'];\r\n\r\n  const revenueStats = [\r\n    { label: 'Total Revenue', value: '$2.4M', change: '+18.2%', positive: true },\r\n    { label: 'Hotel Bookings', value: '$1.8M', change: '+15.7%', positive: true },\r\n    { label: 'Service Fees', value: '$420K', change: '+22.1%', positive: true },\r\n    { label: 'Other Revenue', value: '$180K', change: '****%', positive: true }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 overflow-hidden\">\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6\">\r\n        <div className=\"min-w-0\">\r\n          <h2 className=\"text-lg sm:text-xl font-semibold text-slate-900 truncate\">Revenue Analytics</h2>\r\n          <p className=\"text-slate-600 text-sm\">Detailed breakdown of revenue streams</p>\r\n        </div>\r\n        <div className=\"flex items-center gap-2 overflow-x-auto\">\r\n          {timeframes.map((timeframe) => (\r\n            <button\r\n              key={timeframe}\r\n              onClick={() => setActiveTimeframe(timeframe)}\r\n              className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors whitespace-nowrap ${\r\n                activeTimeframe === timeframe\r\n                  ? 'bg-blue-600 text-white'\r\n                  : 'text-slate-600 hover:bg-slate-100'\r\n              }`}\r\n            >\r\n              {timeframe}\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6\">\r\n        {revenueStats.map((stat, index) => (\r\n          <div key={index} className=\"text-center min-w-0\">\r\n            <p className=\"text-xl sm:text-2xl font-bold text-slate-900 truncate\">{stat.value}</p>\r\n            <p className=\"text-xs sm:text-sm text-slate-600 truncate\">{stat.label}</p>\r\n            <p className={`text-xs sm:text-sm font-medium ${\r\n              stat.positive ? 'text-emerald-600' : 'text-red-600'\r\n            }`}>\r\n              {stat.change}\r\n            </p>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      <div className=\"h-64 sm:h-80 bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl flex items-center justify-center border border-slate-100\">\r\n        <div className=\"text-center p-4\">\r\n          <div className=\"w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4\">\r\n            <i className=\"ri-line-chart-line text-xl sm:text-2xl text-blue-600\"></i>\r\n          </div>\r\n          <p className=\"text-slate-600 font-medium text-sm sm:text-base\">Advanced Revenue Chart</p>\r\n          <p className=\"text-xs sm:text-sm text-slate-500 mt-2\">Interactive analytics with real-time data visualization</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,kOAAQ,EAAC;IAEvD,MAAM,aAAa;QAAC;QAAM;QAAO;QAAO;KAAK;IAE7C,MAAM,eAAe;QACnB;YAAE,OAAO;YAAiB,OAAO;YAAS,QAAQ;YAAU,UAAU;QAAK;QAC3E;YAAE,OAAO;YAAkB,OAAO;YAAS,QAAQ;YAAU,UAAU;QAAK;QAC5E;YAAE,OAAO;YAAgB,OAAO;YAAS,QAAQ;YAAU,UAAU;QAAK;QAC1E;YAAE,OAAO;YAAiB,OAAO;YAAS,QAAQ;YAAS,UAAU;QAAK;KAC3E;IAED,qBACE,+PAAC;QAAI,WAAU;;0BACb,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAG,WAAU;0CAA2D;;;;;;0CACzE,+PAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAExC,+PAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,0BACf,+PAAC;gCAEC,SAAS,IAAM,mBAAmB;gCAClC,WAAW,CAAC,6EAA6E,EACvF,oBAAoB,YAChB,2BACA,qCACJ;0CAED;+BARI;;;;;;;;;;;;;;;;0BAcb,+PAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,+PAAC;wBAAgB,WAAU;;0CACzB,+PAAC;gCAAE,WAAU;0CAAyD,KAAK,KAAK;;;;;;0CAChF,+PAAC;gCAAE,WAAU;0CAA8C,KAAK,KAAK;;;;;;0CACrE,+PAAC;gCAAE,WAAW,CAAC,+BAA+B,EAC5C,KAAK,QAAQ,GAAG,qBAAqB,gBACrC;0CACC,KAAK,MAAM;;;;;;;uBANN;;;;;;;;;;0BAYd,+PAAC;gBAAI,WAAU;0BACb,cAAA,+PAAC;oBAAI,WAAU;;sCACb,+PAAC;4BAAI,WAAU;sCACb,cAAA,+PAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,+PAAC;4BAAE,WAAU;sCAAkD;;;;;;sCAC/D,+PAAC;4BAAE,WAAU;sCAAyC;;;;;;;;;;;;;;;;;;;;;;;AAKhE", "debugId": null}}, {"offset": {"line": 2144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/TopProperties.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\nexport default function TopProperties() {\r\n  const topProperties = [\r\n    {\r\n      name: 'The Ritz-Carlton Downtown',\r\n      bookings: 247,\r\n      revenue: '$189,420',\r\n      rating: 4.9,\r\n      growth: '+28%'\r\n    },\r\n    {\r\n      name: 'Marriott Executive Suites',\r\n      bookings: 198,\r\n      revenue: '$145,830',\r\n      rating: 4.8,\r\n      growth: '+22%'\r\n    },\r\n    {\r\n      name: 'Hilton Garden Inn Premium',\r\n      bookings: 185,\r\n      revenue: '$128,290',\r\n      rating: 4.7,\r\n      growth: '+18%'\r\n    },\r\n    {\r\n      name: 'Courtyard Business Center',\r\n      bookings: 162,\r\n      revenue: '$98,160',\r\n      rating: 4.6,\r\n      growth: '+15%'\r\n    },\r\n    {\r\n      name: 'Holiday Inn Express Plus',\r\n      bookings: 134,\r\n      revenue: '$87,280',\r\n      rating: 4.5,\r\n      growth: '+12%'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 overflow-hidden\">\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6\">\r\n        <h2 className=\"text-lg sm:text-xl font-semibold text-slate-900\">Top Performing Properties</h2>\r\n        <button className=\"text-blue-600 hover:text-blue-700 text-sm font-medium whitespace-nowrap\">\r\n          View All Properties\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"space-y-3 sm:space-y-4\">\r\n        {topProperties.map((property, index) => (\r\n          <div key={index} className=\"flex items-center justify-between p-4 sm:p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors overflow-hidden\">\r\n            <div className=\"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1\">\r\n              <div className=\"w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0\">\r\n                <i className=\"ri-building-2-line text-white text-lg sm:text-xl\"></i>\r\n              </div>\r\n              <div className=\"min-w-0 flex-1\">\r\n                <h3 className=\"font-semibold text-slate-900 text-sm sm:text-base truncate\">{property.name}</h3>\r\n                <div className=\"flex items-center space-x-3 sm:space-x-4 mt-1\">\r\n                  <p className=\"text-xs sm:text-sm text-slate-600 whitespace-nowrap\">{property.bookings} bookings</p>\r\n                  <div className=\"flex items-center whitespace-nowrap\">\r\n                    <i className=\"ri-star-fill text-amber-400 text-xs sm:text-sm mr-1\"></i>\r\n                    <span className=\"text-xs sm:text-sm text-slate-600\">{property.rating}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-right ml-3 flex-shrink-0\">\r\n              <p className=\"font-bold text-slate-900 text-sm sm:text-lg\">{property.revenue}</p>\r\n              <p className=\"text-emerald-600 text-xs sm:text-sm font-medium\">{property.growth}</p>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAIe,SAAS;IACtB,MAAM,gBAAgB;QACpB;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;KACD;IAED,qBACE,+PAAC;QAAI,WAAU;;0BACb,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;wBAAG,WAAU;kCAAkD;;;;;;kCAChE,+PAAC;wBAAO,WAAU;kCAA0E;;;;;;;;;;;;0BAK9F,+PAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,+PAAC;wBAAgB,WAAU;;0CACzB,+PAAC;gCAAI,WAAU;;kDACb,+PAAC;wCAAI,WAAU;kDACb,cAAA,+PAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,+PAAC;wCAAI,WAAU;;0DACb,+PAAC;gDAAG,WAAU;0DAA8D,SAAS,IAAI;;;;;;0DACzF,+PAAC;gDAAI,WAAU;;kEACb,+PAAC;wDAAE,WAAU;;4DAAuD,SAAS,QAAQ;4DAAC;;;;;;;kEACtF,+PAAC;wDAAI,WAAU;;0EACb,+PAAC;gEAAE,WAAU;;;;;;0EACb,+PAAC;gEAAK,WAAU;0EAAqC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAK5E,+PAAC;gCAAI,WAAU;;kDACb,+PAAC;wCAAE,WAAU;kDAA+C,SAAS,OAAO;;;;;;kDAC5E,+PAAC;wCAAE,WAAU;kDAAmD,SAAS,MAAM;;;;;;;;;;;;;uBAlBzE;;;;;;;;;;;;;;;;AAyBpB", "debugId": null}}, {"offset": {"line": 2353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/QuickActions.tsx"], "sourcesContent": ["\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\n// import Button from '../../ui/Button'; // Removed unused import\r\n\r\ntype ColorKey = 'blue' | 'emerald' | 'amber' | 'purple' | 'rose' | 'indigo' | 'cyan' | 'teal' | 'green';\r\ntype PriorityKey = 'high' | 'medium' | 'low';\r\ntype CategoryKey = 'operations' | 'marketing' | 'finance';\r\n\r\ninterface ActionItem {\r\n  title: string;\r\n  description: string;\r\n  icon: string;\r\n  color: ColorKey;\r\n  action: () => void;\r\n  priority: PriorityKey;\r\n}\r\n\r\nexport default function QuickActions() {\r\n  const [activeCategory, setActiveCategory] = useState<CategoryKey>('operations');\r\n\r\n  const actionCategories: Record<CategoryKey, ActionItem[]> = {\r\n    operations: [\r\n      {\r\n        title: 'Add Premium Property',\r\n        description: 'Register luxury hotel partner',\r\n        icon: 'ri-building-2-line',\r\n        color: 'blue',\r\n        action: () => console.log('Add premium property'),\r\n        priority: 'high'\r\n      },\r\n      {\r\n        title: 'Bulk Inventory Update',\r\n        description: 'Update room availability across properties',\r\n        icon: 'ri-database-2-line',\r\n        color: 'emerald',\r\n        action: () => console.log('Bulk inventory update'),\r\n        priority: 'medium'\r\n      },\r\n      {\r\n        title: 'Emergency Maintenance',\r\n        description: 'Schedule system maintenance window',\r\n        icon: 'ri-tools-line',\r\n        color: 'amber',\r\n        action: () => console.log('Emergency maintenance'),\r\n        priority: 'high'\r\n      }\r\n    ],\r\n    marketing: [\r\n      {\r\n        title: 'Launch Campaign',\r\n        description: 'Create targeted marketing campaign',\r\n        icon: 'ri-megaphone-line',\r\n        color: 'purple',\r\n        action: () => console.log('Launch campaign'),\r\n        priority: 'medium'\r\n      },\r\n      {\r\n        title: 'Seasonal Promotions',\r\n        description: 'Set up holiday discount packages',\r\n        icon: 'ri-gift-line',\r\n        color: 'rose',\r\n        action: () => console.log('Seasonal promotions'),\r\n        priority: 'medium'\r\n      },\r\n      {\r\n        title: 'Loyalty Program',\r\n        description: 'Manage premium member benefits',\r\n        icon: 'ri-vip-crown-line',\r\n        color: 'indigo',\r\n        action: () => console.log('Loyalty program'),\r\n        priority: 'low'\r\n      }\r\n    ],\r\n    finance: [\r\n      {\r\n        title: 'Revenue Report',\r\n        description: 'Generate comprehensive financial analysis',\r\n        icon: 'ri-bar-chart-box-line',\r\n        color: 'cyan',\r\n        action: () => console.log('Revenue report'),\r\n        priority: 'high'\r\n      },\r\n      {\r\n        title: 'Payment Gateway',\r\n        description: 'Configure new payment methods',\r\n        icon: 'ri-bank-card-line',\r\n        color: 'teal',\r\n        action: () => console.log('Payment gateway'),\r\n        priority: 'medium'\r\n      },\r\n      {\r\n        title: 'Expense Tracking',\r\n        description: 'Monitor operational costs',\r\n        icon: 'ri-money-dollar-circle-line',\r\n        color: 'green',\r\n        action: () => console.log('Expense tracking'),\r\n        priority: 'low'\r\n      }\r\n    ]\r\n  };\r\n\r\n  const colorClasses: Record<ColorKey, string> = {\r\n    blue: 'bg-blue-50 border-blue-200 hover:bg-blue-100 hover:border-blue-300',\r\n    emerald: 'bg-emerald-50 border-emerald-200 hover:bg-emerald-100 hover:border-emerald-300',\r\n    amber: 'bg-amber-50 border-amber-200 hover:bg-amber-100 hover:border-amber-300',\r\n    purple: 'bg-purple-50 border-purple-200 hover:bg-purple-100 hover:border-purple-300',\r\n    rose: 'bg-rose-50 border-rose-200 hover:bg-rose-100 hover:border-rose-300',\r\n    indigo: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100 hover:border-indigo-300',\r\n    cyan: 'bg-cyan-50 border-cyan-200 hover:bg-cyan-100 hover:border-cyan-300',\r\n    teal: 'bg-teal-50 border-teal-200 hover:bg-teal-100 hover:border-teal-300',\r\n    green: 'bg-green-50 border-green-200 hover:bg-green-100 hover:border-green-300'\r\n  };\r\n\r\n  const iconColors: Record<ColorKey, string> = {\r\n    blue: 'text-blue-600',\r\n    emerald: 'text-emerald-600',\r\n    amber: 'text-amber-600',\r\n    purple: 'text-purple-600',\r\n    rose: 'text-rose-600',\r\n    indigo: 'text-indigo-600',\r\n    cyan: 'text-cyan-600',\r\n    teal: 'text-teal-600',\r\n    green: 'text-green-600'\r\n  };\r\n\r\n  const priorityColors: Record<PriorityKey, string> = {\r\n    high: 'bg-red-500',\r\n    medium: 'bg-amber-500',\r\n    low: 'bg-slate-400'\r\n  };\r\n\r\n  const categories: Array<{ id: CategoryKey; label: string; icon: string }> = [\r\n    { id: 'operations', label: 'Operations', icon: 'ri-settings-line' },\r\n    { id: 'marketing', label: 'Marketing', icon: 'ri-megaphone-line' },\r\n    { id: 'finance', label: 'Finance', icon: 'ri-bar-chart-line' }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden\">\r\n      <div className=\"flex flex-col gap-4 mb-6\">\r\n        <div className=\"min-w-0\">\r\n          <h2 className=\"text-lg sm:text-xl font-semibold text-slate-900\">Quick Actions</h2>\r\n          <p className=\"text-sm text-slate-500\">Streamlined access to key operations</p>\r\n        </div>\r\n        <div className=\"flex items-center gap-1 bg-slate-100 rounded-xl p-1 overflow-x-auto\">\r\n          {categories.map((category) => (\r\n            <button\r\n              key={category.id}\r\n              onClick={() => setActiveCategory(category.id)}\r\n              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all whitespace-nowrap ${\r\n                activeCategory === category.id\r\n                  ? 'bg-white text-slate-900 shadow-sm'\r\n                  : 'text-slate-600 hover:text-slate-900'\r\n              }`}\r\n            >\r\n              <i className={`${category.icon} mr-2`}></i>\r\n              {category.label}\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-1 gap-3 sm:gap-4 mb-6\">\r\n        {actionCategories[activeCategory].map((action, index) => (\r\n          <div\r\n            key={index}\r\n            className={`p-4 sm:p-5 rounded-xl border-2 transition-all cursor-pointer group ${colorClasses[action.color as keyof typeof colorClasses]} overflow-hidden`}\r\n            onClick={action.action}\r\n          >\r\n            <div className=\"flex items-center justify-between gap-3\">\r\n              <div className=\"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1\">\r\n                <div className={`w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-xl bg-white shadow-sm ${iconColors[action.color as keyof typeof iconColors]} flex-shrink-0`}>\r\n                  <i className={`${action.icon} text-lg sm:text-xl`}></i>\r\n                </div>\r\n                <div className=\"min-w-0 flex-1\">\r\n                  <div className=\"flex items-center space-x-2 mb-1\">\r\n                    <h3 className=\"font-semibold text-slate-900 text-sm sm:text-base truncate\">{action.title}</h3>\r\n                    <div className={`w-2 h-2 rounded-full ${priorityColors[action.priority as keyof typeof priorityColors]} flex-shrink-0`}></div>\r\n                  </div>\r\n                  <p className=\"text-sm text-slate-600 line-clamp-2\">{action.description}</p>\r\n                </div>\r\n              </div>\r\n              <div className=\"opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0\">\r\n                <i className=\"ri-arrow-right-line text-slate-400\"></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n      \r\n      <div className=\"space-y-3\">\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n          <button className=\"w-full flex items-center justify-center py-2 sm:py-3 px-4 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors text-sm font-medium\">\r\n            <i className=\"ri-add-line mr-2\"></i>\r\n            Custom Action\r\n          </button>\r\n          <button className=\"w-full flex items-center justify-center py-2 sm:py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium\">\r\n            <i className=\"ri-dashboard-line mr-2\"></i>\r\n            Action Center\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"pt-3 sm:pt-4 border-t border-slate-200\">\r\n          <div className=\"flex items-center justify-between text-sm\">\r\n            <span className=\"text-slate-500\">Frequently Used</span>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                <i className=\"ri-hotel-line text-blue-600 text-sm\"></i>\r\n              </div>\r\n              <div className=\"w-6 h-6 bg-emerald-100 rounded-lg flex items-center justify-center\">\r\n                <i className=\"ri-user-add-line text-emerald-600 text-sm\"></i>\r\n              </div>\r\n              <div className=\"w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center\">\r\n                <i className=\"ri-file-download-line text-purple-600 text-sm\"></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAGA;AAFA;;;AAkBe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,kOAAQ,EAAc;IAElE,MAAM,mBAAsD;QAC1D,YAAY;YACV;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;SACD;QACD,WAAW;YACT;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;SACD;QACD,SAAS;YACP;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,UAAU;YACZ;SACD;IACH;IAEA,MAAM,eAAyC;QAC7C,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,MAAM;QACN,OAAO;IACT;IAEA,MAAM,aAAuC;QAC3C,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,MAAM;QACN,OAAO;IACT;IAEA,MAAM,iBAA8C;QAClD,MAAM;QACN,QAAQ;QACR,KAAK;IACP;IAEA,MAAM,aAAsE;QAC1E;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM;QAAmB;QAClE;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM;QAAoB;QACjE;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM;QAAoB;KAC9D;IAED,qBACE,+PAAC;QAAI,WAAU;;0BACb,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAChE,+PAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAExC,+PAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,+PAAC;gCAEC,SAAS,IAAM,kBAAkB,SAAS,EAAE;gCAC5C,WAAW,CAAC,0EAA0E,EACpF,mBAAmB,SAAS,EAAE,GAC1B,sCACA,uCACJ;;kDAEF,+PAAC;wCAAE,WAAW,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC;;;;;;oCACpC,SAAS,KAAK;;+BATV,SAAS,EAAE;;;;;;;;;;;;;;;;0BAexB,+PAAC;gBAAI,WAAU;0BACZ,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7C,+PAAC;wBAEC,WAAW,CAAC,mEAAmE,EAAE,YAAY,CAAC,OAAO,KAAK,CAA8B,CAAC,gBAAgB,CAAC;wBAC1J,SAAS,OAAO,MAAM;kCAEtB,cAAA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAI,WAAW,CAAC,yFAAyF,EAAE,UAAU,CAAC,OAAO,KAAK,CAA4B,CAAC,cAAc,CAAC;sDAC7K,cAAA,+PAAC;gDAAE,WAAW,GAAG,OAAO,IAAI,CAAC,mBAAmB,CAAC;;;;;;;;;;;sDAEnD,+PAAC;4CAAI,WAAU;;8DACb,+PAAC;oDAAI,WAAU;;sEACb,+PAAC;4DAAG,WAAU;sEAA8D,OAAO,KAAK;;;;;;sEACxF,+PAAC;4DAAI,WAAW,CAAC,qBAAqB,EAAE,cAAc,CAAC,OAAO,QAAQ,CAAgC,CAAC,cAAc,CAAC;;;;;;;;;;;;8DAExH,+PAAC;oDAAE,WAAU;8DAAuC,OAAO,WAAW;;;;;;;;;;;;;;;;;;8CAG1E,+PAAC;oCAAI,WAAU;8CACb,cAAA,+PAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;uBAlBZ;;;;;;;;;;0BAyBX,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAO,WAAU;;kDAChB,+PAAC;wCAAE,WAAU;;;;;;oCAAuB;;;;;;;0CAGtC,+PAAC;gCAAO,WAAU;;kDAChB,+PAAC;wCAAE,WAAU;;;;;;oCAA6B;;;;;;;;;;;;;kCAK9C,+PAAC;wBAAI,WAAU;kCACb,cAAA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAK,WAAU;8CAAiB;;;;;;8CACjC,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAI,WAAU;sDACb,cAAA,+PAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,+PAAC;4CAAI,WAAU;sDACb,cAAA,+PAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,+PAAC;4CAAI,WAAU;sDACb,cAAA,+PAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7B", "debugId": null}}, {"offset": {"line": 2790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/RecentActivity.tsx"], "sourcesContent": ["\r\n'use client';\r\n\r\nimport React from 'react';\r\n\r\nexport default function RecentActivity() {\r\n  const activities = [\r\n    {\r\n      id: 1,\r\n      type: 'booking',\r\n      title: 'High-Value Booking Created',\r\n      message: 'Premium suite booking at The Ritz-Carlton',\r\n      user: '<PERSON>',\r\n      time: '2 minutes ago',\r\n      icon: 'ri-vip-crown-line',\r\n      color: 'emerald',\r\n      amount: '$4,250',\r\n      priority: 'high'\r\n    },\r\n    {\r\n      id: 2,\r\n      type: 'payment',\r\n      title: 'Payment Processing Alert',\r\n      message: 'Large transaction processed successfully',\r\n      user: 'Payment System',\r\n      time: '5 minutes ago',\r\n      icon: 'ri-secure-payment-line',\r\n      color: 'blue',\r\n      amount: '$12,850',\r\n      priority: 'medium'\r\n    },\r\n    {\r\n      id: 3,\r\n      type: 'support',\r\n      title: 'Escalated Ticket Resolved',\r\n      message: 'VIP customer issue resolved - 5-star rating',\r\n      user: '<PERSON>',\r\n      time: '12 minutes ago',\r\n      icon: 'ri-customer-service-2-line',\r\n      color: 'purple',\r\n      amount: null,\r\n      priority: 'high'\r\n    },\r\n    {\r\n      id: 4,\r\n      type: 'hotel',\r\n      title: 'Property Partnership',\r\n      message: 'New luxury hotel added to portfolio',\r\n      user: 'Partnership Team',\r\n      time: '25 minutes ago',\r\n      icon: 'ri-building-2-line',\r\n      color: 'amber',\r\n      amount: null,\r\n      priority: 'medium'\r\n    },\r\n    {\r\n      id: 5,\r\n      type: 'system',\r\n      title: 'Performance Optimization',\r\n      message: 'API response time improved by 23%',\r\n      user: 'Engineering Team',\r\n      time: '1 hour ago',\r\n      icon: 'ri-speed-up-line',\r\n      color: 'cyan',\r\n      amount: null,\r\n      priority: 'low'\r\n    },\r\n    {\r\n      id: 6,\r\n      type: 'user',\r\n      title: 'Premium User Registration',\r\n      message: 'Corporate account created with $50K credit',\r\n      user: 'Sales Team',\r\n      time: '2 hours ago',\r\n      icon: 'ri-user-star-line',\r\n      color: 'rose',\r\n      amount: '$50,000',\r\n      priority: 'high'\r\n    }\r\n  ];\r\n\r\n  const colorClasses = {\r\n    emerald: 'bg-emerald-100 text-emerald-700 border-emerald-200',\r\n    blue: 'bg-blue-100 text-blue-700 border-blue-200',\r\n    purple: 'bg-purple-100 text-purple-700 border-purple-200',\r\n    amber: 'bg-amber-100 text-amber-700 border-amber-200',\r\n    cyan: 'bg-cyan-100 text-cyan-700 border-cyan-200',\r\n    rose: 'bg-rose-100 text-rose-700 border-rose-200'\r\n  };\r\n\r\n  const priorityColors = {\r\n    high: 'bg-red-500',\r\n    medium: 'bg-amber-500',\r\n    low: 'bg-slate-400'\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden\">\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6\">\r\n        <div className=\"min-w-0\">\r\n          <h2 className=\"text-lg sm:text-xl font-semibold text-slate-900\">Live Activity Feed</h2>\r\n          <p className=\"text-sm text-slate-500\">Real-time system and business events</p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-2 flex-shrink-0\">\r\n          <div className=\"w-2 h-2 bg-emerald-500 rounded-full animate-pulse\"></div>\r\n          <span className=\"text-sm text-slate-600\">Live</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-3 sm:space-y-4 max-h-80 sm:max-h-96 overflow-y-auto scrollbar-thin\">\r\n        {activities.map((activity) => (\r\n          <div key={activity.id} className={`p-3 sm:p-4 rounded-xl border-2 transition-all hover:shadow-sm ${colorClasses[activity.color as keyof typeof colorClasses]} overflow-hidden`}>\r\n            <div className=\"flex items-start justify-between gap-3\">\r\n              <div className=\"flex items-start space-x-3 min-w-0 flex-1\">\r\n                <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center border-2 ${colorClasses[activity.color as keyof typeof colorClasses]} flex-shrink-0`}>\r\n                  <i className={`${activity.icon} text-base sm:text-lg`}></i>\r\n                </div>\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"flex items-center space-x-2 mb-1\">\r\n                    <p className=\"font-semibold text-slate-900 text-sm truncate\">{activity.title}</p>\r\n                    <div className={`w-2 h-2 rounded-full ${priorityColors[activity.priority as keyof typeof priorityColors]} flex-shrink-0`}></div>\r\n                  </div>\r\n                  <p className=\"text-sm text-slate-600 mb-2 line-clamp-2\">{activity.message}</p>\r\n                  <div className=\"flex items-center space-x-3 text-xs text-slate-500 overflow-hidden\">\r\n                    <div className=\"flex items-center space-x-1 min-w-0\">\r\n                      <i className=\"ri-user-line flex-shrink-0\"></i>\r\n                      <span className=\"truncate\">{activity.user}</span>\r\n                    </div>\r\n                    <span className=\"flex-shrink-0\">•</span>\r\n                    <div className=\"flex items-center space-x-1 whitespace-nowrap\">\r\n                      <i className=\"ri-time-line\"></i>\r\n                      <span>{activity.time}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"text-right flex-shrink-0\">\r\n                {activity.amount && (\r\n                  <div className=\"font-bold text-slate-900 text-sm mb-1\">{activity.amount}</div>\r\n                )}\r\n                <button className=\"text-xs text-slate-500 hover:text-slate-700 transition-colors whitespace-nowrap\">\r\n                  View Details\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      <div className=\"mt-4 sm:mt-6 pt-4 border-t border-slate-200\">\r\n        <button className=\"w-full flex items-center justify-center space-x-2 py-2 sm:py-3 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-xl transition-colors\">\r\n          <i className=\"ri-refresh-line\"></i>\r\n          <span>Load More Activities</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AACA;;AAIe,SAAS;IACtB,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;KACD;IAED,MAAM,eAAe;QACnB,SAAS;QACT,MAAM;QACN,QAAQ;QACR,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,KAAK;IACP;IAEA,qBACE,+PAAC;QAAI,WAAU;;0BACb,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAChE,+PAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAExC,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAI,WAAU;;;;;;0CACf,+PAAC;gCAAK,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;0BAI7C,+PAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,+PAAC;wBAAsB,WAAW,CAAC,8DAA8D,EAAE,YAAY,CAAC,SAAS,KAAK,CAA8B,CAAC,gBAAgB,CAAC;kCAC5K,cAAA,+PAAC;4BAAI,WAAU;;8CACb,+PAAC;oCAAI,WAAU;;sDACb,+PAAC;4CAAI,WAAW,CAAC,+EAA+E,EAAE,YAAY,CAAC,SAAS,KAAK,CAA8B,CAAC,cAAc,CAAC;sDACzK,cAAA,+PAAC;gDAAE,WAAW,GAAG,SAAS,IAAI,CAAC,qBAAqB,CAAC;;;;;;;;;;;sDAEvD,+PAAC;4CAAI,WAAU;;8DACb,+PAAC;oDAAI,WAAU;;sEACb,+PAAC;4DAAE,WAAU;sEAAiD,SAAS,KAAK;;;;;;sEAC5E,+PAAC;4DAAI,WAAW,CAAC,qBAAqB,EAAE,cAAc,CAAC,SAAS,QAAQ,CAAgC,CAAC,cAAc,CAAC;;;;;;;;;;;;8DAE1H,+PAAC;oDAAE,WAAU;8DAA4C,SAAS,OAAO;;;;;;8DACzE,+PAAC;oDAAI,WAAU;;sEACb,+PAAC;4DAAI,WAAU;;8EACb,+PAAC;oEAAE,WAAU;;;;;;8EACb,+PAAC;oEAAK,WAAU;8EAAY,SAAS,IAAI;;;;;;;;;;;;sEAE3C,+PAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,+PAAC;4DAAI,WAAU;;8EACb,+PAAC;oEAAE,WAAU;;;;;;8EACb,+PAAC;8EAAM,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAK5B,+PAAC;oCAAI,WAAU;;wCACZ,SAAS,MAAM,kBACd,+PAAC;4CAAI,WAAU;sDAAyC,SAAS,MAAM;;;;;;sDAEzE,+PAAC;4CAAO,WAAU;sDAAkF;;;;;;;;;;;;;;;;;;uBA7BhG,SAAS,EAAE;;;;;;;;;;0BAsCzB,+PAAC;gBAAI,WAAU;0BACb,cAAA,+PAAC;oBAAO,WAAU;;sCAChB,+PAAC;4BAAE,WAAU;;;;;;sCACb,+PAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 3163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/SystemHealth.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\nexport default function SystemHealth() {\r\n  const healthMetrics = [\r\n    { label: 'API Response Time', value: '89ms', percentage: 75, color: 'emerald' },\r\n    { label: 'Database Performance', value: '99.2%', percentage: 92, color: 'emerald' },\r\n    { label: 'Payment Gateway', value: 'Online', percentage: 100, color: 'emerald', isStatus: true },\r\n    { label: 'CDN Performance', value: '98.7%', percentage: 87, color: 'emerald' }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden\">\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6\">\r\n        <h3 className=\"font-semibold text-slate-900 text-lg\">System Health Monitor</h3>\r\n        <div className=\"flex items-center space-x-2 flex-shrink-0\">\r\n          <div className=\"w-3 h-3 bg-emerald-500 rounded-full animate-pulse\"></div>\r\n          <span className=\"text-sm text-emerald-600 font-medium\">Optimal</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        {healthMetrics.map((metric, index) => (\r\n          <div key={index} className=\"flex justify-between items-center gap-3\">\r\n            <span className=\"text-sm text-slate-600 min-w-0 flex-1 truncate\">{metric.label}</span>\r\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\r\n              <span className=\"text-sm font-semibold text-emerald-600 whitespace-nowrap\">{metric.value}</span>\r\n              {!metric.isStatus ? (\r\n                <div className=\"w-12 h-2 bg-slate-200 rounded-full\">\r\n                  <div\r\n                    className=\"h-2 bg-emerald-500 rounded-full transition-all duration-300\"\r\n                    style={{ width: `${metric.percentage}%` }}\r\n                  ></div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"w-3 h-3 bg-emerald-500 rounded-full\"></div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAIe,SAAS;IACtB,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAqB,OAAO;YAAQ,YAAY;YAAI,OAAO;QAAU;QAC9E;YAAE,OAAO;YAAwB,OAAO;YAAS,YAAY;YAAI,OAAO;QAAU;QAClF;YAAE,OAAO;YAAmB,OAAO;YAAU,YAAY;YAAK,OAAO;YAAW,UAAU;QAAK;QAC/F;YAAE,OAAO;YAAmB,OAAO;YAAS,YAAY;YAAI,OAAO;QAAU;KAC9E;IAED,qBACE,+PAAC;QAAI,WAAU;;0BACb,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,+PAAC;wBAAI,WAAU;;0CACb,+PAAC;gCAAI,WAAU;;;;;;0CACf,+PAAC;gCAAK,WAAU;0CAAuC;;;;;;;;;;;;;;;;;;0BAI3D,+PAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,+PAAC;wBAAgB,WAAU;;0CACzB,+PAAC;gCAAK,WAAU;0CAAkD,OAAO,KAAK;;;;;;0CAC9E,+PAAC;gCAAI,WAAU;;kDACb,+PAAC;wCAAK,WAAU;kDAA4D,OAAO,KAAK;;;;;;oCACvF,CAAC,OAAO,QAAQ,iBACf,+PAAC;wCAAI,WAAU;kDACb,cAAA,+PAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,OAAO,UAAU,CAAC,CAAC,CAAC;4CAAC;;;;;;;;;;6DAI5C,+PAAC;wCAAI,WAAU;;;;;;;;;;;;;uBAZX;;;;;;;;;;;;;;;;AAoBpB", "debugId": null}}, {"offset": {"line": 3317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/RevenueDistribution.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\nexport default function RevenueDistribution() {\r\n  const revenueData = [\r\n    { source: 'Hotel Commissions', amount: '$1,847,290', percentage: 74, color: 'bg-blue-500' },\r\n    { source: 'Service Fees', amount: '$386,470', percentage: 16, color: 'bg-emerald-500' },\r\n    { source: 'Premium Features', amount: '$189,340', percentage: 8, color: 'bg-purple-500' },\r\n    { source: 'Partnership Revenue', amount: '$89,900', percentage: 2, color: 'bg-amber-500' }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden\">\r\n      <h3 className=\"font-semibold text-slate-900 mb-6 text-lg\">Revenue Distribution</h3>\r\n      <div className=\"space-y-4\">\r\n        {revenueData.map((item, index) => (\r\n          <div key={index}>\r\n            <div className=\"flex justify-between items-center mb-2 gap-3\">\r\n              <div className=\"flex items-center space-x-2 min-w-0 flex-1\">\r\n                <div className={`w-3 h-3 rounded-full ${item.color} flex-shrink-0`}></div>\r\n                <span className=\"text-sm text-slate-600 truncate\">{item.source}</span>\r\n              </div>\r\n              <span className=\"text-sm font-semibold text-slate-900 whitespace-nowrap\">{item.amount}</span>\r\n            </div>\r\n            <div className=\"w-full bg-slate-200 rounded-full h-2\">\r\n              <div\r\n                className={`h-2 rounded-full ${item.color} transition-all duration-500`}\r\n                style={{ width: `${item.percentage}%` }}\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAIe,SAAS;IACtB,MAAM,cAAc;QAClB;YAAE,QAAQ;YAAqB,QAAQ;YAAc,YAAY;YAAI,OAAO;QAAc;QAC1F;YAAE,QAAQ;YAAgB,QAAQ;YAAY,YAAY;YAAI,OAAO;QAAiB;QACtF;YAAE,QAAQ;YAAoB,QAAQ;YAAY,YAAY;YAAG,OAAO;QAAgB;QACxF;YAAE,QAAQ;YAAuB,QAAQ;YAAW,YAAY;YAAG,OAAO;QAAe;KAC1F;IAED,qBACE,+PAAC;QAAI,WAAU;;0BACb,+PAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,+PAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,+PAAC;;0CACC,+PAAC;gCAAI,WAAU;;kDACb,+PAAC;wCAAI,WAAU;;0DACb,+PAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,KAAK,CAAC,cAAc,CAAC;;;;;;0DAClE,+PAAC;gDAAK,WAAU;0DAAmC,KAAK,MAAM;;;;;;;;;;;;kDAEhE,+PAAC;wCAAK,WAAU;kDAA0D,KAAK,MAAM;;;;;;;;;;;;0CAEvF,+PAAC;gCAAI,WAAU;0CACb,cAAA,+PAAC;oCACC,WAAW,CAAC,iBAAiB,EAAE,KAAK,KAAK,CAAC,4BAA4B,CAAC;oCACvE,OAAO;wCAAE,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC;oCAAC;;;;;;;;;;;;uBAXlC;;;;;;;;;;;;;;;;AAmBpB", "debugId": null}}, {"offset": {"line": 3446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/components/home/<USER>/PriorityTasks.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\nexport default function PriorityTasks() {\r\n  const priorityTasks = [\r\n    {\r\n      title: 'Critical: Payment Disputes',\r\n      description: '5 high-value disputes require immediate attention',\r\n      dueTime: 'Due in 1 hour',\r\n      priority: 'high',\r\n      bgColor: 'bg-red-50',\r\n      borderColor: 'border-red-200',\r\n      dotColor: 'bg-red-500',\r\n      textColor: 'text-red-600'\r\n    },\r\n    {\r\n      title: 'Hotel Inventory Sync',\r\n      description: 'Update 47 properties with new availability',\r\n      dueTime: 'Due today',\r\n      priority: 'medium',\r\n      bgColor: 'bg-amber-50',\r\n      borderColor: 'border-amber-200',\r\n      dotColor: 'bg-amber-500',\r\n      textColor: 'text-amber-600'\r\n    },\r\n    {\r\n      title: 'New Partner Approvals',\r\n      description: 'Review and approve 8 new hotel partnerships',\r\n      dueTime: 'Due tomorrow',\r\n      priority: 'low',\r\n      bgColor: 'bg-blue-50',\r\n      borderColor: 'border-blue-200',\r\n      dotColor: 'bg-blue-500',\r\n      textColor: 'text-blue-600'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden\">\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6\">\r\n        <h3 className=\"font-semibold text-slate-900 text-lg\">Priority Tasks</h3>\r\n        <button className=\"text-blue-600 hover:text-blue-700 text-sm font-medium whitespace-nowrap\">\r\n          Manage All\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"space-y-3 sm:space-y-4\">\r\n        {priorityTasks.map((task, index) => (\r\n          <div\r\n            key={index}\r\n            className={`flex items-start space-x-3 p-3 rounded-lg border ${task.bgColor} ${task.borderColor} overflow-hidden`}\r\n          >\r\n            <div className={`w-2 h-2 ${task.dotColor} rounded-full mt-2 flex-shrink-0`}></div>\r\n            <div className=\"flex-1 min-w-0\">\r\n              <p className=\"text-sm font-medium text-slate-900 truncate\">{task.title}</p>\r\n              <p className=\"text-xs text-slate-600 mb-2 line-clamp-2\">{task.description}</p>\r\n              <p className={`text-xs font-medium ${task.textColor}`}>{task.dueTime}</p>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAIe,SAAS;IACtB,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,UAAU;YACV,SAAS;YACT,aAAa;YACb,UAAU;YACV,WAAW;QACb;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,UAAU;YACV,SAAS;YACT,aAAa;YACb,UAAU;YACV,WAAW;QACb;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,UAAU;YACV,SAAS;YACT,aAAa;YACb,UAAU;YACV,WAAW;QACb;KACD;IAED,qBACE,+PAAC;QAAI,WAAU;;0BACb,+PAAC;gBAAI,WAAU;;kCACb,+PAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,+PAAC;wBAAO,WAAU;kCAA0E;;;;;;;;;;;;0BAK9F,+PAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,+PAAC;wBAEC,WAAW,CAAC,iDAAiD,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,gBAAgB,CAAC;;0CAEjH,+PAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,gCAAgC,CAAC;;;;;;0CAC1E,+PAAC;gCAAI,WAAU;;kDACb,+PAAC;wCAAE,WAAU;kDAA+C,KAAK,KAAK;;;;;;kDACtE,+PAAC;wCAAE,WAAU;kDAA4C,KAAK,WAAW;;;;;;kDACzE,+PAAC;wCAAE,WAAW,CAAC,oBAAoB,EAAE,KAAK,SAAS,EAAE;kDAAG,KAAK,OAAO;;;;;;;;;;;;;uBAPjE;;;;;;;;;;;;;;;;AAcjB", "debugId": null}}, {"offset": {"line": 3581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Ecogo/B2C-dashboard/b2c-flight/app/page.tsx"], "sourcesContent": ["\n'use client';\n\nimport LayoutWrapper from \"./components/layout/LayoutWrapper\";\nimport DashboardHeader from \"./components/home/<USER>/DashboardHeader\";\nimport DashboardStats from \"./components/home/<USER>/DashboardStats\";\nimport RevenueAnalytics from \"./components/home/<USER>/RevenueAnalytics\";\nimport TopProperties from \"./components/home/<USER>/TopProperties\";\nimport QuickActions from \"./components/home/<USER>/QuickActions\";\nimport RecentActivity from \"./components/home/<USER>/RecentActivity\";\nimport SystemHealth from \"./components/home/<USER>/SystemHealth\";\nimport RevenueDistribution from \"./components/home/<USER>/RevenueDistribution\";\nimport PriorityTasks from \"./components/home/<USER>/PriorityTasks\";\n\nexport default function Home() {\n  return (\n    <LayoutWrapper>\n      <div className=\"space-y-6\">\n        {/* Header Section */}\n        <DashboardHeader />\n\n        {/* Main Stats */}\n        <DashboardStats />\n\n        {/* Advanced Analytics Section */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Revenue Analytics */}\n            <RevenueAnalytics />\n\n            {/* Performance Metrics */}\n            <TopProperties />\n          </div>\n\n          <div className=\"space-y-6\">\n            <RecentActivity />\n            <QuickActions />\n          </div>\n        </div>\n\n        {/* Bottom Section - Advanced Monitoring */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {/* System Health Monitor */}\n          <SystemHealth />\n\n          {/* Revenue Breakdown */}\n          <RevenueDistribution />\n\n          {/* Priority Tasks */}\n          <PriorityTasks />\n        </div>\n      </div>\n    </LayoutWrapper>\n  );\n}"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,qBACE,+PAAC,yKAAa;kBACZ,cAAA,+PAAC;YAAI,WAAU;;8BAEb,+PAAC,mLAAe;;;;;8BAGhB,+PAAC,+LAAc;;;;;8BAGf,+PAAC;oBAAI,WAAU;;sCACb,+PAAC;4BAAI,WAAU;;8CAEb,+PAAC,uLAAgB;;;;;8CAGjB,+PAAC,qLAAa;;;;;;;;;;;sCAGhB,+PAAC;4BAAI,WAAU;;8CACb,+PAAC,8LAAc;;;;;8CACf,+PAAC,0LAAY;;;;;;;;;;;;;;;;;8BAKjB,+PAAC;oBAAI,WAAU;;sCAEb,+PAAC,oLAAY;;;;;sCAGb,+PAAC,wLAAmB;;;;;sCAGpB,+PAAC,gLAAa;;;;;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}]}