
'use client';

import LayoutWrapper from "./components/layout/LayoutWrapper";
import DashboardHeader from "./components/home/<USER>/DashboardHeader";
import DashboardStats from "./components/home/<USER>/DashboardStats";
import RevenueAnalytics from "./components/home/<USER>/RevenueAnalytics";
import TopProperties from "./components/home/<USER>/TopProperties";
import QuickActions from "./components/home/<USER>/QuickActions";
import RecentActivity from "./components/home/<USER>/RecentActivity";
import SystemHealth from "./components/home/<USER>/SystemHealth";
import RevenueDistribution from "./components/home/<USER>/RevenueDistribution";
import PriorityTasks from "./components/home/<USER>/PriorityTasks";

export default function Home() {
  return (
    <LayoutWrapper>
      <div className="space-y-6">
        {/* Header Section */}
        <DashboardHeader />

        {/* Main Stats */}
        <DashboardStats />

        {/* Advanced Analytics Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Revenue Analytics */}
            <RevenueAnalytics />

            {/* Performance Metrics */}
            <TopProperties />
          </div>

          <div className="space-y-6">
            <RecentActivity />
            <QuickActions />
          </div>
        </div>

        {/* Bottom Section - Advanced Monitoring */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* System Health Monitor */}
          <SystemHealth />

          {/* Revenue Breakdown */}
          <RevenueDistribution />

          {/* Priority Tasks */}
          <PriorityTasks />
        </div>
      </div>
    </LayoutWrapper>
  );
}