(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,29626,(e,t,r)=>{"use strict";function s(e){let t={};for(let[r,s]of e.entries()){let e=t[r];void 0===e?t[r]=s:Array.isArray(e)?e.push(s):t[r]=[e,s]}return t}function l(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,s]of Object.entries(e))if(Array.isArray(s))for(let e of s)t.append(r,l(e));else t.set(r,l(s));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,s]of t.entries())e.append(r,s)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return i},searchParamsToUrlQuery:function(){return s},urlQueryToSearchParams:function(){return a}})},15080,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return a},formatWithValidation:function(){return n},urlObjectKeys:function(){return i}});let s=e.r(13555)._(e.r(29626)),l=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",i=e.pathname||"",n=e.hash||"",o=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),o&&"object"==typeof o&&(o=String(s.urlQueryToSearchParams(o)));let d=e.search||o&&"?"+o||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||l.test(a))&&!1!==c?(c="//"+(c||""),i&&"/"!==i[0]&&(i="/"+i)):c||(c=""),n&&"#"!==n[0]&&(n="#"+n),d&&"?"!==d[0]&&(d="?"+d),""+a+c+(i=i.replace(/[?#]/g,encodeURIComponent))+(d=d.replace("#","%23"))+n}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function n(e){return a(e)}},12432,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return l}});let s=e.r(94528);function l(e,t){let r=(0,s.useRef)(null),l=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=r.current;e&&(r.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(r.current=a(e,s)),t&&(l.current=a(t,s))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},58105,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return f},PageNotFoundError:function(){return b},SP:function(){return u},ST:function(){return h},WEB_VITALS:function(){return s},execOnce:function(){return l},getDisplayName:function(){return c},getLocationOrigin:function(){return n},getURL:function(){return o},isAbsoluteUrl:function(){return i},isResSent:function(){return d},loadGetInitialProps:function(){return x},normalizeRepeatedSlashes:function(){return m},stringifyError:function(){return j}});let s=["CLS","FCP","FID","INP","LCP","TTFB"];function l(e){let t,r=!1;return function(){for(var s=arguments.length,l=Array(s),a=0;a<s;a++)l[a]=arguments[a];return r||(r=!0,t=e(...l)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>a.test(e);function n(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=n();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function d(e){return e.finished||e.headersSent}function m(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function x(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await x(t.Component,t.ctx)}:{};let s=await e.getInitialProps(t);if(r&&d(r))return s;if(!s)throw Object.defineProperty(Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+s+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s}let u="undefined"!=typeof performance,h=u&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class f extends Error{}class b extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function j(e){return JSON.stringify({message:e.message,stack:e.stack})}},19772,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let s=e.r(58105),l=e.r(87101);function a(e){if(!(0,s.isAbsoluteUrl)(e))return!0;try{let t=(0,s.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,l.hasBasePath)(r.pathname)}catch(e){return!1}}},64554,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},25038,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return f},useLinkStatus:function(){return g}});let s=e.r(13555),l=e.r(74591),a=s._(e.r(94528)),i=e.r(15080),n=e.r(69331),o=e.r(12432),c=e.r(58105),d=e.r(55488);e.r(83138);let m=e.r(47195),x=e.r(19772),u=e.r(99419);e.r(64554);let h=e.r(70466);function p(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function f(e){var t;let r,s,i,[f,g]=(0,a.useOptimistic)(m.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:j,as:y,children:N,prefetch:w=null,passHref:k,replace:C,shallow:P,scroll:T,onClick:S,onMouseEnter:M,onTouchStart:E,legacyBehavior:A=!1,onNavigate:R,ref:O,unstable_dynamicOnHover:L,..._}=e;r=N,A&&("string"==typeof r||"number"==typeof r)&&(r=(0,l.jsx)("a",{children:r}));let I=a.default.useContext(n.AppRouterContext),D=!1!==w,U=!1!==w?null===(t=w)||"auto"===t?h.FetchStrategy.PPR:h.FetchStrategy.Full:h.FetchStrategy.PPR,{href:F,as:B}=a.default.useMemo(()=>{let e=p(j);return{href:e,as:y?p(y):e}},[j,y]);A&&(s=a.default.Children.only(r));let $=A?s&&"object"==typeof s&&s.ref:O,z=a.default.useCallback(e=>(null!==I&&(v.current=(0,m.mountLinkInstance)(e,F,I,U,D,g)),()=>{v.current&&((0,m.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,m.unmountPrefetchableInstance)(e)}),[D,F,I,U,g]),K={ref:(0,o.useMergedRef)(z,$),onClick(e){A||"function"!=typeof S||S(e),A&&s.props&&"function"==typeof s.props.onClick&&s.props.onClick(e),I&&(e.defaultPrevented||function(e,t,r,s,l,i,n){let{nodeName:o}=e.currentTarget;if(!("A"===o.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,x.isLocalURL)(t)){l&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),n){let e=!1;if(n({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,u.dispatchNavigateAction)(r||t,l?"replace":"push",null==i||i,s.current)})}}(e,F,B,v,C,T,R))},onMouseEnter(e){A||"function"!=typeof M||M(e),A&&s.props&&"function"==typeof s.props.onMouseEnter&&s.props.onMouseEnter(e),I&&D&&(0,m.onNavigationIntent)(e.currentTarget,!0===L)},onTouchStart:function(e){A||"function"!=typeof E||E(e),A&&s.props&&"function"==typeof s.props.onTouchStart&&s.props.onTouchStart(e),I&&D&&(0,m.onNavigationIntent)(e.currentTarget,!0===L)}};return(0,c.isAbsoluteUrl)(B)?K.href=B:A&&!k&&("a"!==s.type||"href"in s.props)||(K.href=(0,d.addBasePath)(B)),i=A?a.default.cloneElement(s,K):(0,l.jsx)("a",{..._,...K,children:r}),(0,l.jsx)(b.Provider,{value:f,children:i})}let b=(0,a.createContext)(m.IDLE_LINK_STATUS),g=()=>(0,a.useContext)(b);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},16487,e=>{"use strict";e.s(["default",()=>f],16487);var t=e.i(74591),r=e.i(94528);function s(){let[e,s]=(0,r.useState)(""),[l,a]=(0,r.useState)(!1),[i,n]=(0,r.useState)(!1),[o,c]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{c(window.innerWidth<768)},t=e=>{e.target.closest(".dropdown-container")||(a(!1),n(!1))};return e(),window.addEventListener("resize",e),document.addEventListener("click",t),()=>{window.removeEventListener("resize",e),document.removeEventListener("click",t)}},[]),(0,t.jsx)("nav",{className:"header px-responsive py-3 shadow-sm flex-shrink-0 h-16 w-full",style:{backgroundColor:"var(--color-header-bg)",borderBottom:"1px solid var(--color-header-border)"},children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 lg:space-x-6 flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center",style:{background:"linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))"},children:(0,t.jsx)("i",{className:"ri-building-2-fill text-white text-sm"})}),!o&&(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-pacifico text-responsive-lg",style:{color:"var(--color-text-primary)"},children:"TravelAdmin"}),(0,t.jsx)("div",{className:"text-xs",style:{color:"var(--color-text-tertiary)"},children:"Enterprise Dashboard"})]})]}),(0,t.jsxs)("div",{className:"relative flex-1 max-w-sm lg:max-w-md",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-search-line text-sm",style:{color:"var(--color-text-muted)"}})}),(0,t.jsx)("input",{type:"text",placeholder:o?"Search...":"Search bookings, users, hotels...",value:e,onChange:e=>s(e.target.value),className:"input pl-9 pr-3 py-2 w-full text-responsive-sm placeholder-opacity-75 transition-all",style:{backgroundColor:"var(--color-surface-alt)",border:"1px solid var(--color-border)",color:"var(--color-text-primary)"}}),!o&&(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-2 flex items-center",children:(0,t.jsx)("kbd",{className:"inline-flex items-center rounded px-1.5 py-0.5 text-xs font-medium",style:{color:"var(--color-text-tertiary)",backgroundColor:"var(--color-surface)"},children:"⌘K"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 lg:space-x-4",children:[!o&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1.5 rounded-lg border",style:{backgroundColor:"rgba(34, 197, 94, 0.1)",borderColor:"rgba(34, 197, 94, 0.2)"},children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full animate-pulse",style:{backgroundColor:"var(--color-success-500)"}}),(0,t.jsx)("span",{className:"text-xs font-medium",style:{color:"var(--color-success-700)"},children:"All Systems Operational"}),(0,t.jsx)("div",{className:"text-xs",style:{color:"var(--color-success-600)"},children:"99.9%"})]}),!o&&(0,t.jsxs)("div",{className:"flex items-center space-x-3 px-3 py-1.5 rounded-lg",style:{backgroundColor:"var(--color-surface-alt)"},children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-responsive-sm font-bold",style:{color:"var(--color-text-primary)"},children:"$42.8K"}),(0,t.jsx)("div",{className:"text-xs",style:{color:"var(--color-text-tertiary)"},children:"Today"})]}),(0,t.jsx)("div",{className:"w-px h-6",style:{backgroundColor:"var(--color-border)"}}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-responsive-sm font-bold",style:{color:"var(--color-text-primary)"},children:"847"}),(0,t.jsx)("div",{className:"text-xs",style:{color:"var(--color-text-tertiary)"},children:"Active"})]})]}),(0,t.jsxs)("div",{className:"relative dropdown-container",children:[(0,t.jsxs)("button",{onClick:()=>a(!l),className:"relative p-2 rounded-lg transition-colors touch-target",style:{color:"var(--color-text-secondary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--color-surface-alt)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,t.jsx)("i",{className:"ri-notification-line text-lg"}),(0,t.jsx)("span",{className:"absolute -top-1 -right-1 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-medium",style:{backgroundColor:"var(--color-error-500)"},children:"3"})]}),l&&(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-80 rounded-xl shadow-xl border z-50 modal-responsive animate-scale-in",style:{backgroundColor:"var(--color-modal-bg)",borderColor:"var(--color-border)"},children:[(0,t.jsx)("div",{className:"p-4 border-b",style:{borderColor:"var(--color-border)"},children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"font-semibold",style:{color:"var(--color-text-primary)"},children:"Notifications"}),(0,t.jsx)("button",{className:"text-sm font-medium",style:{color:"var(--color-primary-600)"},children:"Mark all read"})]})}),(0,t.jsx)("div",{className:"max-h-64 overflow-y-auto",children:[{icon:"ri-alert-line",iconColor:"var(--color-error-600)",iconBg:"rgba(220, 38, 38, 0.15)",title:"Payment Gateway Alert",message:"High volume of failed transactions detected",time:"2 minutes ago",unread:!0},{icon:"ri-customer-service-line",iconColor:"var(--color-warning-600)",iconBg:"rgba(217, 119, 6, 0.15)",title:"Support Queue Alert",message:"15 tickets pending - priority response needed",time:"8 minutes ago",unread:!0},{icon:"ri-building-line",iconColor:"var(--color-primary-600)",iconBg:"rgba(37, 99, 235, 0.15)",title:"New Partner Integration",message:"Marriott Group has completed API integration",time:"1 hour ago",unread:!1}].map((e,r)=>(0,t.jsx)("div",{className:"p-3 border-b transition-colors cursor-pointer",style:{borderColor:"var(--color-border-light)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--color-surface-alt)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center",style:{backgroundColor:e.iconBg},children:(0,t.jsx)("i",{className:"".concat(e.icon," text-sm"),style:{color:e.iconColor}})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium",style:{color:"var(--color-text-primary)"},children:e.title}),(0,t.jsx)("p",{className:"text-xs mt-1",style:{color:"var(--color-text-secondary)"},children:e.message}),(0,t.jsx)("p",{className:"text-xs mt-1",style:{color:"var(--color-text-muted)"},children:e.time})]}),e.unread&&(0,t.jsx)("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:"var(--color-error-500)"}})]})},r))}),(0,t.jsx)("div",{className:"p-3 border-t",style:{borderColor:"var(--color-border)"},children:(0,t.jsx)("button",{className:"w-full text-center text-sm font-medium",style:{color:"var(--color-primary-600)"},children:"View all notifications"})})]})]}),(0,t.jsxs)("div",{className:"relative dropdown-container",children:[(0,t.jsxs)("button",{onClick:()=>n(!i),className:"flex items-center space-x-2 p-1.5 rounded-lg transition-colors touch-target",style:{},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--color-surface-alt)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center",style:{background:"linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))"},children:(0,t.jsx)("span",{className:"text-white text-xs font-semibold",children:"JD"})}),!o&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"text-sm font-semibold",style:{color:"var(--color-text-primary)"},children:"John Doe"}),(0,t.jsx)("p",{className:"text-xs",style:{color:"var(--color-text-tertiary)"},children:"Operations Manager"})]}),(0,t.jsx)("i",{className:"ri-arrow-down-s-line",style:{color:"var(--color-text-muted)"}})]})]}),i&&(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-56 rounded-xl shadow-xl border z-50 animate-scale-in",style:{backgroundColor:"var(--color-modal-bg)",borderColor:"var(--color-border)"},children:[(0,t.jsx)("div",{className:"p-3 border-b",style:{borderColor:"var(--color-border)"},children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center",style:{background:"linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700))"},children:(0,t.jsx)("span",{className:"text-white font-semibold text-sm",children:"JD"})}),(0,t.jsxs)("div",{className:"min-w-0",children:[(0,t.jsx)("p",{className:"font-semibold text-sm",style:{color:"var(--color-text-primary)"},children:"John Doe"}),(0,t.jsx)("p",{className:"text-xs",style:{color:"var(--color-text-tertiary)"},children:"Operations Manager"}),(0,t.jsx)("p",{className:"text-xs",style:{color:"var(--color-text-muted)"},children:"Premium Access"})]})]})}),(0,t.jsxs)("div",{className:"py-1",children:[[{icon:"ri-user-line",label:"Profile Settings"},{icon:"ri-settings-line",label:"Account Preferences"},{icon:"ri-shield-user-line",label:"Security Settings"}].map((e,r)=>(0,t.jsxs)("a",{href:"#",className:"flex items-center space-x-2 px-3 py-2 text-sm transition-colors",style:{color:"var(--color-text-secondary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--color-surface-alt)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,t.jsx)("i",{className:e.icon,style:{color:"var(--color-text-muted)"}}),(0,t.jsx)("span",{children:e.label})]},r)),(0,t.jsx)("div",{className:"border-t my-1",style:{borderColor:"var(--color-border)"}}),(0,t.jsxs)("a",{href:"#",className:"flex items-center space-x-2 px-3 py-2 text-sm transition-colors",style:{color:"var(--color-text-secondary)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="var(--color-surface-alt)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[(0,t.jsx)("i",{className:"ri-logout-box-line",style:{color:"var(--color-text-muted)"}}),(0,t.jsx)("span",{children:"Sign Out"})]})]})]})]})]})]})})}var l=e.i(25038);function a(e){let{isCollapsed:s=!1,onToggleCollapse:a,isMobile:i=!1}=e,[n,o]=(0,r.useState)("Dashboard");return(0,t.jsxs)("div",{className:"sidebar h-full transition-all duration-300 ".concat(s?"w-16":"w-64"," flex flex-col shadow-xl flex-shrink-0 overflow-hidden"),style:{backgroundColor:"var(--color-sidebar-bg)",color:"var(--color-sidebar-text)",borderRight:"1px solid var(--color-sidebar-border)"},children:[(0,t.jsx)("div",{className:"p-4 flex-shrink-0",style:{borderBottom:"1px solid var(--color-sidebar-border)"},children:(0,t.jsxs)("button",{onClick:a,className:"w-full flex items-center justify-between rounded-lg p-2 transition-colors touch-target hover:bg-opacity-10",style:{color:"var(--color-sidebar-text)"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="rgba(203, 213, 225, 0.1)"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[!s&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-semibold text-responsive-sm",style:{color:"var(--color-sidebar-text)"},children:"Navigation Menu"}),(0,t.jsx)("p",{className:"text-xs mt-0.5 opacity-75",children:"Enterprise Dashboard"})]}),(0,t.jsx)("i",{className:"ri-menu-fold-line opacity-75 ".concat(s?"rotate-180":""," transition-transform text-lg")})]})}),(0,t.jsx)("div",{className:"flex-1 sidebar-scroll-container",children:(0,t.jsxs)("div",{className:"overflow-y-auto py-2 sidebar-scrollbar h-full",children:[(0,t.jsx)("div",{className:"px-2 space-y-1",children:[{name:"Dashboard",icon:"ri-dashboard-line",href:"/",active:!0},{name:"Hotel Management",icon:"ri-hotel-line",href:"/hotel-master",active:!1},{name:"Bookings & Cancellations",icon:"ri-calendar-check-line",href:"/booking",active:!1},{name:"Hotel Providers",icon:"ri-building-2-line",href:"/hotel-provider",active:!1},{name:"Markup Management",icon:"ri-price-tag-3-line",href:"/markup",active:!1},{name:"Payments",icon:"ri-bank-card-line",href:"/payments",active:!1},{name:"User Management",icon:"ri-user-settings-line",href:"/users",active:!1},{name:"Promotions",icon:"ri-discount-percent-line",href:"/promotions",active:!1},{name:"Support Tickets",icon:"ri-customer-service-2-line",href:"/support",active:!1},{name:"Content Management",icon:"ri-file-text-line",href:"/content",active:!1},{name:"Reports",icon:"ri-bar-chart-line",href:"/reports",active:!1},{name:"System Settings",icon:"ri-settings-3-line",href:"/settings",active:!1}].map(e=>(0,t.jsx)(l.default,{href:e.href,children:(0,t.jsxs)("div",{className:"sidebar-item flex items-center px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 touch-target ".concat(n===e.name?"active":""),onClick:()=>o(e.name),style:{backgroundColor:n===e.name?"var(--color-primary-600)":"transparent",color:n===e.name?"var(--color-sidebar-text-active)":"var(--color-sidebar-text)"},onMouseEnter:t=>{n!==e.name&&(t.currentTarget.style.backgroundColor="rgba(203, 213, 225, 0.1)",t.currentTarget.style.color="var(--color-sidebar-text-active)")},onMouseLeave:t=>{n!==e.name&&(t.currentTarget.style.backgroundColor="transparent",t.currentTarget.style.color="var(--color-sidebar-text)")},children:[(0,t.jsx)("div",{className:"w-5 h-5 flex items-center justify-center flex-shrink-0",children:(0,t.jsx)("i",{className:"".concat(e.icon," text-base")})}),!s&&(0,t.jsx)("span",{className:"text-responsive-sm font-medium ml-3 truncate",children:e.name}),!s&&i&&(0,t.jsx)("i",{className:"ri-arrow-right-s-line ml-auto opacity-50"})]})},e.name))}),!s&&(0,t.jsxs)("div",{className:"px-4 mt-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("div",{className:"text-xs font-semibold uppercase tracking-wider opacity-75",children:"Future Modules"}),(0,t.jsx)("div",{className:"px-2 py-0.5 rounded-full",style:{backgroundColor:"rgba(203, 213, 225, 0.2)"},children:(0,t.jsx)("span",{className:"text-xs opacity-75",children:"Soon"})})]}),(0,t.jsx)("div",{className:"space-y-1",children:[{name:"Flight Management",icon:"ri-flight-takeoff-line",href:"/flights",disabled:!0},{name:"Cruise Management",icon:"ri-ship-line",href:"/cruises",disabled:!0},{name:"Holiday Packages",icon:"ri-suitcase-line",href:"/packages",disabled:!0}].map(e=>(0,t.jsxs)("div",{className:"flex items-center px-3 py-2.5 rounded-lg cursor-not-allowed opacity-50 touch-target",children:[(0,t.jsx)("div",{className:"w-5 h-5 flex items-center justify-center flex-shrink-0",children:(0,t.jsx)("i",{className:"".concat(e.icon," text-base")})}),(0,t.jsx)("span",{className:"text-responsive-sm font-medium ml-3 truncate",children:e.name})]},e.name))})]})]})}),(0,t.jsx)("div",{className:"p-4 flex-shrink-0",style:{borderTop:"1px solid var(--color-sidebar-border)"},children:(0,t.jsxs)("div",{className:"flex items-center ".concat(s?"justify-center":"space-x-3"),children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0",style:{backgroundColor:"var(--color-success-600)"},children:(0,t.jsx)("i",{className:"ri-shield-check-line text-white text-sm"})}),!s&&(0,t.jsxs)("div",{className:"min-w-0",children:[(0,t.jsx)("p",{className:"text-responsive-sm font-medium truncate",style:{color:"var(--color-sidebar-text)"},children:"System Status"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-0.5",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full animate-pulse flex-shrink-0",style:{backgroundColor:"var(--color-success-400)"}}),(0,t.jsx)("p",{className:"text-xs truncate",style:{color:"var(--color-success-400)"},children:"All Systems Online"})]})]})]})})]})}function i(e){let{children:l}=e,[i,n]=(0,r.useState)(!1),[o,c]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;c(e),e&&n(!0)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,t.jsxs)("div",{className:"h-screen w-full bg-slate-50 flex flex-col overflow-hidden",children:[(0,t.jsx)("header",{className:"w-full h-16 flex-shrink-0 z-40 bg-white border-b",children:(0,t.jsx)(s,{})}),(0,t.jsxs)("div",{className:"flex w-full h-[calc(100vh-4rem)]",children:[(0,t.jsx)("aside",{className:"".concat(o?i?"w-0":"w-64":i?"w-16":"w-64"," flex-shrink-0 transition-all duration-300 ease-in-out relative z-30 h-full ").concat(o&&i?"overflow-hidden":""),children:(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(a,{isCollapsed:i,onToggleCollapse:()=>n(!i),isMobile:o})})}),(0,t.jsx)("main",{className:"flex-1 overflow-y-auto overflow-x-hidden custom-scrollbar bg-slate-50 h-full",children:(0,t.jsx)("div",{className:"p-4 sm:p-6",children:l})})]}),o&&!i&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-20",onClick:()=>n(!0)})]})}function n(){return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-slate-900 mb-1",children:"Executive Dashboard"}),(0,t.jsx)("p",{className:"text-slate-600 text-sm sm:text-base",children:"Comprehensive overview of your travel platform operations"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("button",{className:"px-3 py-2 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors text-sm font-medium whitespace-nowrap",children:[(0,t.jsx)("i",{className:"ri-download-line mr-2"}),"Export Data"]}),(0,t.jsxs)("button",{className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium whitespace-nowrap",children:[(0,t.jsx)("i",{className:"ri-add-line mr-2"}),"Quick Action"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-4 sm:p-6 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)("p",{className:"text-blue-100 text-xs sm:text-sm font-medium",children:"Live Performance"}),(0,t.jsx)("p",{className:"text-xl sm:text-2xl font-bold truncate",children:"98.7%"}),(0,t.jsx)("p",{className:"text-blue-100 text-xs sm:text-sm",children:"System Uptime"})]}),(0,t.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-blue-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3",children:(0,t.jsx)("i",{className:"ri-pulse-line text-xl sm:text-2xl"})})]})}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-xl p-4 sm:p-6 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)("p",{className:"text-emerald-100 text-xs sm:text-sm font-medium",children:"Active Sessions"}),(0,t.jsx)("p",{className:"text-xl sm:text-2xl font-bold truncate",children:"2,847"}),(0,t.jsx)("p",{className:"text-emerald-100 text-xs sm:text-sm",children:"Current Users"})]}),(0,t.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-emerald-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3",children:(0,t.jsx)("i",{className:"ri-user-line text-xl sm:text-2xl"})})]})}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl p-4 sm:p-6 text-white sm:col-span-2 lg:col-span-1",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)("p",{className:"text-purple-100 text-xs sm:text-sm font-medium",children:"Today's Revenue"}),(0,t.jsx)("p",{className:"text-xl sm:text-2xl font-bold truncate",children:"$89.2K"}),(0,t.jsx)("p",{className:"text-purple-100 text-xs sm:text-sm",children:"+12.3% vs yesterday"})]}),(0,t.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-purple-500/30 rounded-xl flex items-center justify-center flex-shrink-0 ml-3",children:(0,t.jsx)("i",{className:"ri-trending-up-line text-xl sm:text-2xl"})})]})})]})]})}function o(){let e={blue:"from-blue-500 to-blue-600",emerald:"from-emerald-500 to-emerald-600",purple:"from-purple-500 to-purple-600",amber:"from-amber-500 to-amber-600",cyan:"from-cyan-500 to-cyan-600",rose:"from-rose-500 to-rose-600"},r={blue:"bg-blue-50 border-blue-100",emerald:"bg-emerald-50 border-emerald-100",purple:"bg-purple-50 border-purple-100",amber:"bg-amber-50 border-amber-100",cyan:"bg-cyan-50 border-cyan-100",rose:"bg-rose-50 border-rose-100"};return(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[{title:"Total Bookings",value:"47,892",change:"+18.2%",changeType:"positive",icon:"ri-calendar-check-fill",color:"blue",trend:"up",subtitle:"This month vs last month",additionalInfo:"2,847 today"},{title:"Gross Revenue",value:"$8.7M",change:"+24.5%",changeType:"positive",icon:"ri-money-dollar-circle-fill",color:"emerald",trend:"up",subtitle:"Monthly recurring revenue",additionalInfo:"$289K today"},{title:"Active Properties",value:"2,847",change:"+12.3%",changeType:"positive",icon:"ri-building-2-fill",color:"purple",trend:"up",subtitle:"Verified hotel partners",additionalInfo:"47 new this week"},{title:"Customer Satisfaction",value:"4.89",change:"+0.12",changeType:"positive",icon:"ri-star-fill",color:"amber",trend:"up",subtitle:"Average rating score",additionalInfo:"12,847 reviews"},{title:"Support Resolution",value:"97.2%",change:"****%",changeType:"positive",icon:"ri-customer-service-2-fill",color:"cyan",trend:"up",subtitle:"First contact resolution",additionalInfo:"Avg 4.2 min response"},{title:"Conversion Rate",value:"12.8%",change:"****%",changeType:"positive",icon:"ri-arrow-up-circle-fill",color:"rose",trend:"up",subtitle:"Visitor to booking ratio",additionalInfo:"Above industry avg"}].map((s,l)=>(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 hover:shadow-md transition-shadow ".concat(r[s.color]," overflow-hidden"),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3 sm:mb-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br ".concat(e[s.color]," rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0"),children:(0,t.jsx)("i",{className:"".concat(s.icon," text-white text-lg sm:text-xl")})}),(0,t.jsx)("div",{className:"text-right ml-2",children:(0,t.jsxs)("div",{className:"inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ".concat("positive"===s.changeType?"bg-emerald-100 text-emerald-700":"bg-red-100 text-red-700"," whitespace-nowrap"),children:[(0,t.jsx)("i",{className:"".concat("positive"===s.changeType?"ri-arrow-up-line":"ri-arrow-down-line"," mr-1")}),s.change]})})]}),(0,t.jsxs)("div",{className:"mb-3 min-w-0",children:[(0,t.jsx)("h3",{className:"text-2xl sm:text-3xl font-bold text-slate-900 mb-1 truncate",children:s.value}),(0,t.jsx)("p",{className:"text-sm font-medium text-slate-600 truncate",children:s.title})]}),(0,t.jsxs)("div",{className:"space-y-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-xs text-slate-500 truncate",children:s.subtitle}),(0,t.jsx)("p",{className:"text-xs text-slate-400 truncate",children:s.additionalInfo})]}),(0,t.jsx)("div",{className:"mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-slate-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-xs text-slate-500",children:"Trend"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-emerald-400 rounded-full"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-emerald-300 rounded-full"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-slate-200 rounded-full"})]})]})})]},l))})}function c(){let[e,s]=(0,r.useState)("7D");return(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"min-w-0",children:[(0,t.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-slate-900 truncate",children:"Revenue Analytics"}),(0,t.jsx)("p",{className:"text-slate-600 text-sm",children:"Detailed breakdown of revenue streams"})]}),(0,t.jsx)("div",{className:"flex items-center gap-2 overflow-x-auto",children:["7D","30D","90D","1Y"].map(r=>(0,t.jsx)("button",{onClick:()=>s(r),className:"px-3 py-2 text-sm font-medium rounded-lg transition-colors whitespace-nowrap ".concat(e===r?"bg-blue-600 text-white":"text-slate-600 hover:bg-slate-100"),children:r},r))})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6",children:[{label:"Total Revenue",value:"$2.4M",change:"+18.2%",positive:!0},{label:"Hotel Bookings",value:"$1.8M",change:"+15.7%",positive:!0},{label:"Service Fees",value:"$420K",change:"+22.1%",positive:!0},{label:"Other Revenue",value:"$180K",change:"****%",positive:!0}].map((e,r)=>(0,t.jsxs)("div",{className:"text-center min-w-0",children:[(0,t.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-slate-900 truncate",children:e.value}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-slate-600 truncate",children:e.label}),(0,t.jsx)("p",{className:"text-xs sm:text-sm font-medium ".concat(e.positive?"text-emerald-600":"text-red-600"),children:e.change})]},r))}),(0,t.jsx)("div",{className:"h-64 sm:h-80 bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl flex items-center justify-center border border-slate-100",children:(0,t.jsxs)("div",{className:"text-center p-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("i",{className:"ri-line-chart-line text-xl sm:text-2xl text-blue-600"})}),(0,t.jsx)("p",{className:"text-slate-600 font-medium text-sm sm:text-base",children:"Advanced Revenue Chart"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-slate-500 mt-2",children:"Interactive analytics with real-time data visualization"})]})})]})}function d(){return(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 lg:p-8 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,t.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-slate-900",children:"Top Performing Properties"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-700 text-sm font-medium whitespace-nowrap",children:"View All Properties"})]}),(0,t.jsx)("div",{className:"space-y-3 sm:space-y-4",children:[{name:"The Ritz-Carlton Downtown",bookings:247,revenue:"$189,420",rating:4.9,growth:"+28%"},{name:"Marriott Executive Suites",bookings:198,revenue:"$145,830",rating:4.8,growth:"+22%"},{name:"Hilton Garden Inn Premium",bookings:185,revenue:"$128,290",rating:4.7,growth:"+18%"},{name:"Courtyard Business Center",bookings:162,revenue:"$98,160",rating:4.6,growth:"+15%"},{name:"Holiday Inn Express Plus",bookings:134,revenue:"$87,280",rating:4.5,growth:"+12%"}].map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 sm:p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1",children:[(0,t.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,t.jsx)("i",{className:"ri-building-2-line text-white text-lg sm:text-xl"})}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-slate-900 text-sm sm:text-base truncate",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 mt-1",children:[(0,t.jsxs)("p",{className:"text-xs sm:text-sm text-slate-600 whitespace-nowrap",children:[e.bookings," bookings"]}),(0,t.jsxs)("div",{className:"flex items-center whitespace-nowrap",children:[(0,t.jsx)("i",{className:"ri-star-fill text-amber-400 text-xs sm:text-sm mr-1"}),(0,t.jsx)("span",{className:"text-xs sm:text-sm text-slate-600",children:e.rating})]})]})]})]}),(0,t.jsxs)("div",{className:"text-right ml-3 flex-shrink-0",children:[(0,t.jsx)("p",{className:"font-bold text-slate-900 text-sm sm:text-lg",children:e.revenue}),(0,t.jsx)("p",{className:"text-emerald-600 text-xs sm:text-sm font-medium",children:e.growth})]})]},r))})]})}function m(){let[e,s]=(0,r.useState)("operations"),l={blue:"bg-blue-50 border-blue-200 hover:bg-blue-100 hover:border-blue-300",emerald:"bg-emerald-50 border-emerald-200 hover:bg-emerald-100 hover:border-emerald-300",amber:"bg-amber-50 border-amber-200 hover:bg-amber-100 hover:border-amber-300",purple:"bg-purple-50 border-purple-200 hover:bg-purple-100 hover:border-purple-300",rose:"bg-rose-50 border-rose-200 hover:bg-rose-100 hover:border-rose-300",indigo:"bg-indigo-50 border-indigo-200 hover:bg-indigo-100 hover:border-indigo-300",cyan:"bg-cyan-50 border-cyan-200 hover:bg-cyan-100 hover:border-cyan-300",teal:"bg-teal-50 border-teal-200 hover:bg-teal-100 hover:border-teal-300",green:"bg-green-50 border-green-200 hover:bg-green-100 hover:border-green-300"},a={blue:"text-blue-600",emerald:"text-emerald-600",amber:"text-amber-600",purple:"text-purple-600",rose:"text-rose-600",indigo:"text-indigo-600",cyan:"text-cyan-600",teal:"text-teal-600",green:"text-green-600"},i={high:"bg-red-500",medium:"bg-amber-500",low:"bg-slate-400"};return(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"min-w-0",children:[(0,t.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-slate-900",children:"Quick Actions"}),(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Streamlined access to key operations"})]}),(0,t.jsx)("div",{className:"flex items-center gap-1 bg-slate-100 rounded-xl p-1 overflow-x-auto",children:[{id:"operations",label:"Operations",icon:"ri-settings-line"},{id:"marketing",label:"Marketing",icon:"ri-megaphone-line"},{id:"finance",label:"Finance",icon:"ri-bar-chart-line"}].map(r=>(0,t.jsxs)("button",{onClick:()=>s(r.id),className:"px-3 py-2 rounded-lg text-sm font-medium transition-all whitespace-nowrap ".concat(e===r.id?"bg-white text-slate-900 shadow-sm":"text-slate-600 hover:text-slate-900"),children:[(0,t.jsx)("i",{className:"".concat(r.icon," mr-2")}),r.label]},r.id))})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3 sm:gap-4 mb-6",children:({operations:[{title:"Add Premium Property",description:"Register luxury hotel partner",icon:"ri-building-2-line",color:"blue",action:()=>console.log("Add premium property"),priority:"high"},{title:"Bulk Inventory Update",description:"Update room availability across properties",icon:"ri-database-2-line",color:"emerald",action:()=>console.log("Bulk inventory update"),priority:"medium"},{title:"Emergency Maintenance",description:"Schedule system maintenance window",icon:"ri-tools-line",color:"amber",action:()=>console.log("Emergency maintenance"),priority:"high"}],marketing:[{title:"Launch Campaign",description:"Create targeted marketing campaign",icon:"ri-megaphone-line",color:"purple",action:()=>console.log("Launch campaign"),priority:"medium"},{title:"Seasonal Promotions",description:"Set up holiday discount packages",icon:"ri-gift-line",color:"rose",action:()=>console.log("Seasonal promotions"),priority:"medium"},{title:"Loyalty Program",description:"Manage premium member benefits",icon:"ri-vip-crown-line",color:"indigo",action:()=>console.log("Loyalty program"),priority:"low"}],finance:[{title:"Revenue Report",description:"Generate comprehensive financial analysis",icon:"ri-bar-chart-box-line",color:"cyan",action:()=>console.log("Revenue report"),priority:"high"},{title:"Payment Gateway",description:"Configure new payment methods",icon:"ri-bank-card-line",color:"teal",action:()=>console.log("Payment gateway"),priority:"medium"},{title:"Expense Tracking",description:"Monitor operational costs",icon:"ri-money-dollar-circle-line",color:"green",action:()=>console.log("Expense tracking"),priority:"low"}]})[e].map((e,r)=>(0,t.jsx)("div",{className:"p-4 sm:p-5 rounded-xl border-2 transition-all cursor-pointer group ".concat(l[e.color]," overflow-hidden"),onClick:e.action,children:(0,t.jsxs)("div",{className:"flex items-center justify-between gap-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1",children:[(0,t.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-xl bg-white shadow-sm ".concat(a[e.color]," flex-shrink-0"),children:(0,t.jsx)("i",{className:"".concat(e.icon," text-lg sm:text-xl")})}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-slate-900 text-sm sm:text-base truncate",children:e.title}),(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(i[e.priority]," flex-shrink-0")})]}),(0,t.jsx)("p",{className:"text-sm text-slate-600 line-clamp-2",children:e.description})]})]}),(0,t.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0",children:(0,t.jsx)("i",{className:"ri-arrow-right-line text-slate-400"})})]})},r))}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,t.jsxs)("button",{className:"w-full flex items-center justify-center py-2 sm:py-3 px-4 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors text-sm font-medium",children:[(0,t.jsx)("i",{className:"ri-add-line mr-2"}),"Custom Action"]}),(0,t.jsxs)("button",{className:"w-full flex items-center justify-center py-2 sm:py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium",children:[(0,t.jsx)("i",{className:"ri-dashboard-line mr-2"}),"Action Center"]})]}),(0,t.jsx)("div",{className:"pt-3 sm:pt-4 border-t border-slate-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-slate-500",children:"Frequently Used"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-hotel-line text-blue-600 text-sm"})}),(0,t.jsx)("div",{className:"w-6 h-6 bg-emerald-100 rounded-lg flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-user-add-line text-emerald-600 text-sm"})}),(0,t.jsx)("div",{className:"w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-file-download-line text-purple-600 text-sm"})})]})]})})]})]})}function x(){let e={emerald:"bg-emerald-100 text-emerald-700 border-emerald-200",blue:"bg-blue-100 text-blue-700 border-blue-200",purple:"bg-purple-100 text-purple-700 border-purple-200",amber:"bg-amber-100 text-amber-700 border-amber-200",cyan:"bg-cyan-100 text-cyan-700 border-cyan-200",rose:"bg-rose-100 text-rose-700 border-rose-200"},r={high:"bg-red-500",medium:"bg-amber-500",low:"bg-slate-400"};return(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"min-w-0",children:[(0,t.jsx)("h2",{className:"text-lg sm:text-xl font-semibold text-slate-900",children:"Live Activity Feed"}),(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Real-time system and business events"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-sm text-slate-600",children:"Live"})]})]}),(0,t.jsx)("div",{className:"space-y-3 sm:space-y-4 max-h-80 sm:max-h-96 overflow-y-auto scrollbar-thin",children:[{id:1,type:"booking",title:"High-Value Booking Created",message:"Premium suite booking at The Ritz-Carlton",user:"Sarah Johnson",time:"2 minutes ago",icon:"ri-vip-crown-line",color:"emerald",amount:"$4,250",priority:"high"},{id:2,type:"payment",title:"Payment Processing Alert",message:"Large transaction processed successfully",user:"Payment System",time:"5 minutes ago",icon:"ri-secure-payment-line",color:"blue",amount:"$12,850",priority:"medium"},{id:3,type:"support",title:"Escalated Ticket Resolved",message:"VIP customer issue resolved - 5-star rating",user:"Mike Chen",time:"12 minutes ago",icon:"ri-customer-service-2-line",color:"purple",amount:null,priority:"high"},{id:4,type:"hotel",title:"Property Partnership",message:"New luxury hotel added to portfolio",user:"Partnership Team",time:"25 minutes ago",icon:"ri-building-2-line",color:"amber",amount:null,priority:"medium"},{id:5,type:"system",title:"Performance Optimization",message:"API response time improved by 23%",user:"Engineering Team",time:"1 hour ago",icon:"ri-speed-up-line",color:"cyan",amount:null,priority:"low"},{id:6,type:"user",title:"Premium User Registration",message:"Corporate account created with $50K credit",user:"Sales Team",time:"2 hours ago",icon:"ri-user-star-line",color:"rose",amount:"$50,000",priority:"high"}].map(s=>(0,t.jsx)("div",{className:"p-3 sm:p-4 rounded-xl border-2 transition-all hover:shadow-sm ".concat(e[s.color]," overflow-hidden"),children:(0,t.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-3 min-w-0 flex-1",children:[(0,t.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center border-2 ".concat(e[s.color]," flex-shrink-0"),children:(0,t.jsx)("i",{className:"".concat(s.icon," text-base sm:text-lg")})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("p",{className:"font-semibold text-slate-900 text-sm truncate",children:s.title}),(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(r[s.priority]," flex-shrink-0")})]}),(0,t.jsx)("p",{className:"text-sm text-slate-600 mb-2 line-clamp-2",children:s.message}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 text-xs text-slate-500 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1 min-w-0",children:[(0,t.jsx)("i",{className:"ri-user-line flex-shrink-0"}),(0,t.jsx)("span",{className:"truncate",children:s.user})]}),(0,t.jsx)("span",{className:"flex-shrink-0",children:"•"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 whitespace-nowrap",children:[(0,t.jsx)("i",{className:"ri-time-line"}),(0,t.jsx)("span",{children:s.time})]})]})]})]}),(0,t.jsxs)("div",{className:"text-right flex-shrink-0",children:[s.amount&&(0,t.jsx)("div",{className:"font-bold text-slate-900 text-sm mb-1",children:s.amount}),(0,t.jsx)("button",{className:"text-xs text-slate-500 hover:text-slate-700 transition-colors whitespace-nowrap",children:"View Details"})]})]})},s.id))}),(0,t.jsx)("div",{className:"mt-4 sm:mt-6 pt-4 border-t border-slate-200",children:(0,t.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 py-2 sm:py-3 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-xl transition-colors",children:[(0,t.jsx)("i",{className:"ri-refresh-line"}),(0,t.jsx)("span",{children:"Load More Activities"})]})})]})}function u(){return(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-slate-900 text-lg",children:"System Health Monitor"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-emerald-500 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-sm text-emerald-600 font-medium",children:"Optimal"})]})]}),(0,t.jsx)("div",{className:"space-y-4",children:[{label:"API Response Time",value:"89ms",percentage:75,color:"emerald"},{label:"Database Performance",value:"99.2%",percentage:92,color:"emerald"},{label:"Payment Gateway",value:"Online",percentage:100,color:"emerald",isStatus:!0},{label:"CDN Performance",value:"98.7%",percentage:87,color:"emerald"}].map((e,r)=>(0,t.jsxs)("div",{className:"flex justify-between items-center gap-3",children:[(0,t.jsx)("span",{className:"text-sm text-slate-600 min-w-0 flex-1 truncate",children:e.label}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,t.jsx)("span",{className:"text-sm font-semibold text-emerald-600 whitespace-nowrap",children:e.value}),e.isStatus?(0,t.jsx)("div",{className:"w-3 h-3 bg-emerald-500 rounded-full"}):(0,t.jsx)("div",{className:"w-12 h-2 bg-slate-200 rounded-full",children:(0,t.jsx)("div",{className:"h-2 bg-emerald-500 rounded-full transition-all duration-300",style:{width:"".concat(e.percentage,"%")}})})]})]},r))})]})}function h(){return(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,t.jsx)("h3",{className:"font-semibold text-slate-900 mb-6 text-lg",children:"Revenue Distribution"}),(0,t.jsx)("div",{className:"space-y-4",children:[{source:"Hotel Commissions",amount:"$1,847,290",percentage:74,color:"bg-blue-500"},{source:"Service Fees",amount:"$386,470",percentage:16,color:"bg-emerald-500"},{source:"Premium Features",amount:"$189,340",percentage:8,color:"bg-purple-500"},{source:"Partnership Revenue",amount:"$89,900",percentage:2,color:"bg-amber-500"}].map((e,r)=>(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2 gap-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 min-w-0 flex-1",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.color," flex-shrink-0")}),(0,t.jsx)("span",{className:"text-sm text-slate-600 truncate",children:e.source})]}),(0,t.jsx)("span",{className:"text-sm font-semibold text-slate-900 whitespace-nowrap",children:e.amount})]}),(0,t.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full ".concat(e.color," transition-all duration-500"),style:{width:"".concat(e.percentage,"%")}})})]},r))})]})}function p(){return(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-slate-200 p-4 sm:p-6 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-slate-900 text-lg",children:"Priority Tasks"}),(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-700 text-sm font-medium whitespace-nowrap",children:"Manage All"})]}),(0,t.jsx)("div",{className:"space-y-3 sm:space-y-4",children:[{title:"Critical: Payment Disputes",description:"5 high-value disputes require immediate attention",dueTime:"Due in 1 hour",priority:"high",bgColor:"bg-red-50",borderColor:"border-red-200",dotColor:"bg-red-500",textColor:"text-red-600"},{title:"Hotel Inventory Sync",description:"Update 47 properties with new availability",dueTime:"Due today",priority:"medium",bgColor:"bg-amber-50",borderColor:"border-amber-200",dotColor:"bg-amber-500",textColor:"text-amber-600"},{title:"New Partner Approvals",description:"Review and approve 8 new hotel partnerships",dueTime:"Due tomorrow",priority:"low",bgColor:"bg-blue-50",borderColor:"border-blue-200",dotColor:"bg-blue-500",textColor:"text-blue-600"}].map((e,r)=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.bgColor," ").concat(e.borderColor," overflow-hidden"),children:[(0,t.jsx)("div",{className:"w-2 h-2 ".concat(e.dotColor," rounded-full mt-2 flex-shrink-0")}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-slate-900 truncate",children:e.title}),(0,t.jsx)("p",{className:"text-xs text-slate-600 mb-2 line-clamp-2",children:e.description}),(0,t.jsx)("p",{className:"text-xs font-medium ".concat(e.textColor),children:e.dueTime})]})]},r))})]})}function f(){return(0,t.jsx)(i,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(n,{}),(0,t.jsx)(o,{}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)(c,{}),(0,t.jsx)(d,{})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(x,{}),(0,t.jsx)(m,{})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsx)(u,{}),(0,t.jsx)(h,{}),(0,t.jsx)(p,{})]})]})})}}]);